apiVersion: apps/v1
kind: Deployment
metadata:
  name: rex-web
  labels:
    app: rex-web
spec:
  replicas: 2
  selector:
    matchLabels:
      app: rex-web
  template:
    metadata:
      labels:
        app: rex-web
      annotations:
        deploy-timestamp: "${TIMESTAMP}"  # force rollout when image is unchanged
    spec:
      containers:
        - name: rex-web
          image: 541428895748.dkr.ecr.ap-southeast-4.amazonaws.com/rex-prod-web:${IMAGE_TAG}
          imagePullPolicy: Always
          ports:
            - containerPort: 5173
          envFrom:
            - configMapRef:
                name: rex-env-config-web
          resources:
            requests:
              cpu: 100m
              memory: 256Mi
            limits:
              cpu: 500m
              memory: 512Mi
