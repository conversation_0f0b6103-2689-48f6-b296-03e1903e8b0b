apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: rex-web-ingress
  namespace: rex
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - app.irex.com.au
      secretName: rex-web-tls
  rules:
    - host: app.irex.com.au
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: rex-web-service
                port:
                  number: 80
