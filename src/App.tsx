import { BrowserRouter, Route, Routes, useLocation } from 'react-router-dom';
import { Suspense, lazy, useEffect, useState } from 'react';
import { MainLayout, RegisterLayout } from '@Components/Layout';
import { MainLoader } from '@Components/Common';
import { PageNotFound } from '@Pages';
import { RoleGuard } from '@Helpers/Roleguard';
import { publicRoutes, privateRoutes } from './Config/Routes.Config';
import { PATHS } from './Config/Path.Config';
import { useSelector } from 'react-redux';
import AdobePdfViewer from './Pages/Forms&Documents/PDFViewer';
import Redirecting from './Pages/Redirecting';
import { Modal, Button } from '@Components/UI';
import { useNavigate } from 'react-router';

const Subscribe = lazy(() => import('./Pages/Subscribe'));
import InternetConnectionMonitor from './Pages/InternetConnectionMonitor';
import { useLocalStorageListener } from '@Query/Hooks/useLocalStorageListener';
const TermsConditionsPublic = lazy(
  () => import('./Pages/TermsConditionsPublic')
);
const PrivacyPolicyPublic = lazy(() => import('./Pages/PrivacyPolicyPublic'));
const AboutUsPublic = lazy(() => import('./Pages/AboutUsPublic'));
const LicenseCheck = lazy(() => import('./Pages/LicenseCheck'));
const Disclaimer = lazy(() => import('./Pages/Disclaimer'));

interface LayoutDescription {
  title: string;
  subTitle: string;
  showBack: boolean;
}

const RegLayoutDesc: Record<string, LayoutDescription> = {
  '/login': {
    title: 'Welcome to REX',
    subTitle: 'Please enter your details',
    showBack: false,
  },
  '/sign-up': { title: 'Create your account', subTitle: '', showBack: false },
  '/verify-otp': {
    title: 'Verification',
    subTitle:
      'Please enter the OTP sent to the registered mobile number to verify your mobile number.',
    showBack: true,
  },
};

function App() {
  return (
    <BrowserRouter>
      <AppContent />
    </BrowserRouter>
  );
}

function AppContent() {
  const navigate = useNavigate();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state.UserControle.user);
  const myValue = useLocalStorageListener('LICENSE_MSG');
  const { pathname } = useLocation(); // Now useLocation() is inside BrowserRouter
  const [showLicenseCheck, setShowLicenseCheck] = useState(false);
  const [showLicenseResubmitted, setShowLicenseResubmitted] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const isRejected = userData?.license_status === 'rejected';

    const isPrivateRoute = privateRoutes.some(
      (route) => route.path === pathname
    );

    setShowLicenseCheck(isRejected && isPrivateRoute);
  }, [pathname, userData]);

  useEffect(() => {
    const isResubmitted = userData?.license_status === 'resubmitted';
    const isPrivateRoute = privateRoutes.some(
      (route) => route.path === pathname
    );

    const isProfilePage = pathname === PATHS.PROFILE;
    const isEnterpriseBusinessDetails =
      pathname === PATHS.ENTERPRISE_BUSINESS_DETAILS;
    if (
      isResubmitted &&
      isPrivateRoute &&
      !isProfilePage &&
      !isEnterpriseBusinessDetails
    ) {
      setShowLicenseResubmitted(true);
    }
  }, [pathname, userData]);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isOnline) {
    return <InternetConnectionMonitor />;
  }

  return (
    <>
      <Routes>
        <Route element={<RoleGuard isPublicRoute={true} />}>
          {publicRoutes.map((route) => {
            const Component = route.element;
            return (
              <Route
                key={route.path}
                path={route.path}
                element={
                  <Suspense fallback={<MainLoader />}>
                    <RegisterLayout
                      title={RegLayoutDesc[route.path]?.title ?? ''}
                      subTitle={RegLayoutDesc[route.path]?.subTitle ?? ''}
                      showBack={RegLayoutDesc[route.path]?.showBack ?? false}
                    >
                      <Component />
                    </RegisterLayout>
                  </Suspense>
                }
              />
            );
          })}
        </Route>

        <Route element={<RoleGuard isPublicRoute={false} />}>
          {privateRoutes
            .filter((route) => {
              if (route.roles && userData && userData.role) {
                return route.roles.includes(userData.role);
              }
              return true;
            })
            .map((route) => {
              const Component = route.element;
              return (
                <Route
                  key={route.path}
                  path={route.path}
                  element={
                    <Suspense fallback={<MainLoader />}>
                      <MainLayout>
                        <Component />
                      </MainLayout>
                    </Suspense>
                  }
                />
              );
            })}
          <Route
            path={PATHS.SUBSCRIBE}
            element={
              <Suspense fallback={<MainLoader />}>
                <Subscribe />
              </Suspense>
            }
          />
        </Route>

        <Route
          path={PATHS.DISCLAIMER}
          element={
            <Suspense fallback={<MainLoader />}>
              <MainLayout>
                <Disclaimer />
              </MainLayout>
            </Suspense>
          }
        />

        <Route
          path={PATHS.TERMS_CONDITIONS_PUBLIC}
          element={
            <Suspense fallback={<MainLoader />}>
              <TermsConditionsPublic />
            </Suspense>
          }
        />
        <Route
          path={PATHS.PRIVACY_POLICY_PUBLIC}
          element={
            <Suspense fallback={<MainLoader />}>
              <PrivacyPolicyPublic />
            </Suspense>
          }
        />
        <Route
          path={PATHS.ABOUT_US_PUBLIC}
          element={
            <Suspense fallback={<MainLoader />}>
              <AboutUsPublic />
            </Suspense>
          }
        />

        <Route
          path={'/show-pdf'}
          element={
            // <Suspense fallback={<MainLoader />}>
            <AdobePdfViewer />
            // </Suspense>
          }
        />

        <Route path={'/redirection/:id/:token'} element={<Redirecting />} />

        {/* 404 Page */}
        <Route path="*" element={<PageNotFound />} />
      </Routes>

      {showLicenseCheck &&
        (userData?.role === 'sub-user' ? (
          <Modal isOpen={showLicenseCheck} hideCloseButton>
            <div className="p-4 text-center items-center justify-center">
              <h4 className="text-black text-2xl font-bold">
                License Rejected
              </h4>
              <p className="mt-4 text-gray-700">{myValue}</p>
              <Button
                className="mt-4"
                text="View Profile"
                type="button"
                onClick={() => {
                  setShowLicenseCheck(false);
                  navigate(PATHS.PROFILE);
                }}
              />
            </div>
          </Modal>
        ) : (
          <LicenseCheck
            isOpen={true}
            onClose={() => setShowLicenseCheck(false)}
          />
        ))}

      {showLicenseResubmitted && (
        <Modal isOpen={showLicenseResubmitted} hideCloseButton>
          <div className="p-4 text-center items-center justify-center">
            <h4 className="text-black text-2xl font-bold">
              License Resubmitted
            </h4>
            <p className="mt-4 text-gray-700">
              {userData?.role === 'sub-user'
                ? myValue
                : `Your license number has been resubmitted for approval. You can't
              access the app until it's approved by an admin.`}
            </p>
            <Button
              className="mt-4"
              text="View Profile"
              type="button"
              onClick={() => {
                setShowLicenseResubmitted(false);
                navigate(PATHS.PROFILE);
              }}
            />
          </div>
        </Modal>
      )}
    </>
  );
}

export default App;
