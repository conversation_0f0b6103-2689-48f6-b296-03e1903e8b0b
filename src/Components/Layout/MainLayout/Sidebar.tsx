import Logo from '@Assets/Images/RexLogo.png';
import MenuItem from './MenuItem';
import { useEffect, useState } from 'react';
import {
  BellIcon,
  ChartLineUpIcon,
  ClockCounterwiseIcon,
  DatabaseIcon,
  FileIcon,
  HouseIcon,
  QuestionsIcon,
  UserCircleIcon,
  VideoIcon,
  EnterpriseIcon,
} from '@Icons';
// import { useNavigate } from 'react-router';
// import { PATHS } from '@Config/Path.Config';
import { AccessibleImage } from '@Components/UI';
import clsx from 'clsx';
import { PATHS } from '@Config/Path.Config';
import { useLocation, matchPath } from 'react-router';
import { useSelector } from 'react-redux';

const Sidebar: React.FC = () => {
  // const navigate = useNavigate();
  const { pathname } = useLocation();

  interface UserData {
    first_name?: string;
    last_name?: string;
    role?: string;
  }

  const [isCollapsed, setIsCollapsed] = useState(false); // Track collapse state
  const [activeMenu, setActiveMenu] = useState('home');

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state.UserControle.user);

  const getFullName = (user: UserData | null): string => {
    if (!user || !user.first_name) return 'Guest';

    const firstName = user.first_name;
    return firstName.length > 15 ? `${firstName.slice(0, 15)}...` : firstName;
  };

  const isAdmin = userData?.role === 'org-admin';

  const getMenuItems = (): MenuItem[] => {
    const items: MenuItem[] = [
      { key: 'home', title: 'Home', Icon: HouseIcon, path: '/', active: true },
      {
        key: 'history',
        title: 'History',
        Icon: ClockCounterwiseIcon,
        path: PATHS.HISTORY,
        active: false,
      },
      {
        key: 'training&Education',
        title: 'Training & Education',
        Icon: VideoIcon,
        path: PATHS.TRAINING,
        active: false,
      },
      {
        key: 'forms&documents',
        title: 'Forms & Documents',
        Icon: FileIcon,
        path: PATHS.FORMSANDDOC,
        active: false,
      },
      {
        key: 'analyticData',
        title: 'Analytic Data',
        Icon: ChartLineUpIcon,
        path: PATHS.ANALYTICS,
        active: false,
      },
      {
        key: 'legal',
        title: 'Legal',
        Icon: DatabaseIcon,
        path: PATHS.LEGALS,
        active: false,
      },
      {
        key: 'buildersHub',
        title: 'Builder’s Hub',
        Icon: QuestionsIcon,
        path: PATHS.BUILDER_HUB,
        active: false,
      },
      {
        key: 'notifications',
        title: 'Notifications',
        Icon: BellIcon,
        path: PATHS.NOTIFICATION,
        active: false,
        bottom: true,
      },
      {
        key: 'account',
        title: getFullName(userData),
        Icon: UserCircleIcon,
        path: PATHS.PROFILE,
        active: false,
        bottom: true,
      },
    ];

    // Only add Enterprise Management if user is admin
    if (isAdmin) {
      items.splice(7, 0, {
        key: 'enterprise',
        title: 'Enterprise Management',
        Icon: EnterpriseIcon,
        path: PATHS.ENTERPRISE,
        active: false,
      });
    }

    return items;
  };

  const menuItems = getMenuItems();

  useEffect(() => {
    if (pathname) {
      if (
        [
          PATHS.MY_SUBSCRIPTION,
          PATHS.ABOUT_US,
          PATHS.CONTACT_US,
          PATHS.TERMS_CONDITIONS,
          PATHS.NOTIFICATION_SETTING,
          PATHS.PRIVACY_POLICY,
        ].includes(pathname)
      ) {
        setActiveMenu('account');
      } else if (
        matchPath(PATHS.TRAINING_VIDEOS, pathname) ||
        matchPath(PATHS.WATCH_VIDEO, pathname) ||
        matchPath(PATHS.QUIZ, pathname)
      ) {
        setActiveMenu('training&Education');
      } else if (pathname?.split('/').includes('builders-hub')) {
        setActiveMenu('buildersHub');
      } else {
        const activeSB = menuItems.find((el) => el.path === pathname);
        if (activeSB) setActiveMenu(activeSB.key);
      }
    }
  }, [pathname]);

  const toggleSidebar = () => {
    setIsCollapsed((prevState) => !prevState); // Toggle the collapse state
  };

  return (
    <div
      className={clsx(
        'h-screen  flex flex-col  text-white shadow-lg transition-all duration-300',
        isCollapsed ? 'w-[86px]' : 'w-[342px]'
      )}
    >
      {/* Logo Section */}
      <div
        className={clsx(
          'flex items-center h-[120px] cursor-pointer',
          isCollapsed ? 'mx-auto' : 'ml-6'
        )}
        // onClick={() => navigate(PATHS.HOME)}
        onClick={toggleSidebar}
      >
        <AccessibleImage
          src={Logo}
          alt="logo"
          className={`${isCollapsed ? 'w-[50px]' : 'w-[124px] h-[76px]'} h-auto ${
            isCollapsed ? 'hidden' : 'block'
          }`}
        />
      </div>

      {/* Navigation Menu */}
      <nav
        className={clsx(
          'flex-grow overflow-y-auto custom-scrollbar',
          isCollapsed ? 'pt-0' : 'pt-4'
        )}
      >
        <ul>
          {menuItems
            .filter((i) => !i.bottom)
            .map(({ key, title, Icon, path }) => (
              <MenuItem
                key={key}
                keyName={key}
                title={title}
                Icon={Icon}
                activeMenu={activeMenu}
                setActiveMenu={setActiveMenu}
                showNotificationBadge
                notificationCount={20}
                showCaretIcon
                isCollapsed={isCollapsed}
                path={path}
              />
            ))}
        </ul>
      </nav>

      {/* Bottom Section */}
      <div>
        <ul>
          {menuItems
            .filter((i) => i.bottom)
            .map(({ key, title, Icon, path }) => (
              <MenuItem
                key={key}
                keyName={key}
                title={title}
                Icon={Icon}
                activeMenu={activeMenu}
                setActiveMenu={setActiveMenu}
                showNotificationBadge={key === 'notifications'}
                showCaretIcon={key === 'account'}
                notificationCount={
                  key === 'notifications' ? userData?.notifications_count : 0
                } // Adjust this based on your logic
                isCollapsed={isCollapsed}
                path={path}
              />
            ))}
        </ul>
      </div>
    </div>
  );
};

export default Sidebar;
