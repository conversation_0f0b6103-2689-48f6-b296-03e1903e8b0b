import React, { ReactNode, useEffect, useState } from 'react';
import Sidebar from './Sidebar';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router';
import { API_PATHS, MODULES_PATHS } from '@Helpers/Constants';
import { Button } from '@Components/UI';
import { UpgradeIcon } from '@Icons';
import { PATHS, PLANNED_PROTECTED_ROUTES } from '@Config/Path.Config';
import { setUserData } from '@Redux/SystemControl/UserControle';
import Api from '@Helpers/Api';
import TempComponent from '@Components/Common/TempComponent';
import clsx from 'clsx';

interface MainLayoytInterface {
  children: ReactNode;
}

type Feature = {
  id: number;
  name: string;
  display_name: string;
};

const isFeatureAvailable = (
  featureList: Feature[],
  modulePath: string
): boolean => {
  const splittedPath = modulePath.split('/');
  if (
    splittedPath.includes('category') ||
    splittedPath.includes('video') ||
    splittedPath.includes('quiz')
  ) {
    modulePath = PATHS.TRAINING;
  }
  if (PLANNED_PROTECTED_ROUTES.includes(modulePath)) {
    return featureList?.some(
      (feature) => feature.name === MODULES_PATHS[modulePath]
    );
  } else return true;
};

const MainLayout: React.FC<MainLayoytInterface> = ({ children }) => {
  const features = useSelector(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (state: any) => state?.UserControle?.user
  );
  const api = new Api();
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  console.log('modulePa', pathname);

  const [isProfileLoading, setIsProfileLoading] = useState<boolean>(true);

  useEffect(() => {
    if (pathname === PATHS.HOME) {
      setTimeout(async () => {
        try {
          const { data } = await api.get(API_PATHS.USER_PROFILE);
          if (data?.status) {
            if (
              !data?.data?.is_subscribed &&
              !data?.data?.is_subscription_cancel &&
              data?.data?.role === 'user'
            ) {
              navigate(PATHS.SUBSCRIBE);
            }
            dispatch(setUserData({ ...features, ...data?.data }));
          }
        } catch (err) {
          console.log('err', err);
        } finally {
          setIsProfileLoading(false);
        }
      }, 1000);
    } else {
      setIsProfileLoading(false);
    }
  }, [features?.is_subscribed, pathname]);

  return (
    <div className="h-screen w-screen max-w-screen max-h-screen flex flex-1 overflow-hidden relative">
      <Sidebar />
      <div
        className={clsx(
          'h-full w-full flex flex-col relative',
          id ? '!overflow-hidden' : '!overflow-auto '
        )}
      >
        {isFeatureAvailable(features?.subscription_data?.features, pathname) &&
          children}

        {!isProfileLoading &&
          !isFeatureAvailable(
            features?.subscription_data?.features,
            pathname
          ) && (
            <>
              <TempComponent />
              {/* Just blur effect without dark overlay */}
              <div className="absolute inset-0 backdrop-blur-lg z-50 pointer-events-none" />
              {/* Modal on top of the blur */}
              <div className="absolute inset-0 flex items-center justify-center z-50">
                <div className="bg-white rounded-2xl shadow-primary p-6 w-[90%] max-w-md text-center">
                  <div className="flex flex-col gap-6 text-center justify-center items-center">
                    <UpgradeIcon height={64} width={64} />
                    <div className="text-center px-6 text-secondary text-xl ">
                      Your plan doesn't have access to this feature. Upgrade
                      your plan to enjoy this feature seamlessly.
                    </div>
                    <Button
                      text="Upgrade"
                      type="button"
                      onClick={() => {
                        navigate(PATHS.MY_SUBSCRIPTION);
                      }}
                    />
                  </div>
                </div>
              </div>
            </>
          )}
      </div>
    </div>
  );
};

export default MainLayout;
