/* eslint-disable */

import { useState, useRef, useEffect, SetStateAction, Dispatch } from 'react';
import { CloseIcon, MicIcon, MicOffIcon, TickIcon } from '@Icons';

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
  type SpeechRecognition = any;
  interface SpeechRecognitionEvent extends Event {
    resultIndex: number;
    results: SpeechRecognitionResultList;
  }
}

interface RecorderProps {
  setIsRecordOn: Dispatch<SetStateAction<boolean>>;
  onClose: (voiceText: string) => void;
}

const Recorder = ({ setIsRecordOn, onClose }: RecorderProps): JSX.Element => {
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [transcript, setTranscript] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunks = useRef<BlobPart[]>([]);
  const recognitionRef = useRef<any>(null);
  const streamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      const SpeechRecognition =
        window.SpeechRecognition || window.webkitSpeechRecognition;

      const recognition = new (window.SpeechRecognition ||
        window.webkitSpeechRecognition)();

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onstart = () => setIsRecording(true);
      recognition.onend = () => setIsRecording(false);
      recognition.onerror = (event: { error: string }) => setError(event.error);
      recognition.onresult = (event: SpeechRecognitionEvent) => {
        let newTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];

          if (result.isFinal) {
            newTranscript += result[0].transcript + ' ';
          }
        }

        if (newTranscript.trim()) {
          setTranscript(
            (prevTranscript) => prevTranscript + ' ' + newTranscript
          );
        }
      };

      recognitionRef.current = recognition;
    }

    // Cleanup function to ensure mic is stopped when component unmounts
    return () => {
      stopMicrophoneAndRecognition();
    };
  }, []);

  const stopMicrophoneAndRecognition = () => {
    // Stop media recorder if active
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state !== 'inactive'
    ) {
      mediaRecorderRef.current.stop();
    }

    // Stop speech recognition if active
    if (recognitionRef.current) {
      try {
        recognitionRef.current.stop();
      } catch (e) {
        // Ignore errors that might occur if recognition wasn't started
      }
    }

    // Stop and release the media stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => {
        track.stop();
      });
      streamRef.current = null;
    }

    setIsRecording(false);
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      streamRef.current = stream;
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) audioChunks.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunks.current, { type: 'audio/wav' });
        const url = URL.createObjectURL(audioBlob);
        setAudioUrl(url);
        audioChunks.current = [];
      };

      mediaRecorder.start();
      setIsRecording(true);

      if (recognitionRef.current) recognitionRef.current.start();
    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
    }
    setIsRecording(false);

    if (recognitionRef.current) recognitionRef.current.stop();
  };

  const handleClose = () => {
    stopMicrophoneAndRecognition();
    setIsRecordOn(false);
  };

  const handleSubmit = () => {
    stopMicrophoneAndRecognition();
    onClose(transcript);
    setIsRecordOn(false);
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden outline-none focus:outline-none max-h-screen">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black opacity-80"></div>
      <div className="flex relative flex-col z-50 flex-1 items-center justify-center">
        <div className="relative flex items-center justify-center">
          {isRecording && (
            <div className="absolute w-16 h-16 bg-primary-light opacity-50 rounded-full animate-ping"></div>
          )}
          <MicIcon height={56} width={56} />
        </div>

        <div className="fixed w-full items-center justify-center flex text-center bottom-0 py-6 text-secondary gap-x-4">
          <div
            className="p-4 bg-primary-light flex items-center justify-center rounded-full cursor-pointer"
            onClick={isRecording ? stopRecording : startRecording}
          >
            {isRecording ? (
              <MicIcon
                height={24}
                width={24}
                className={isRecording ? 'text-red-500' : ''}
                fill={!isRecording ? '#fb2c36' : '#00d9b1'}
              />
            ) : (
              <MicOffIcon
                height={24}
                width={24}
                className={isRecording ? 'text-red-500' : ''}
                fill={!isRecording ? '#fb2c36' : '#00d9b1'}
              />
            )}
          </div>
          {transcript && !error && (
            <div
              className="p-4 bg-primary-light flex items-center justify-center rounded-full cursor-pointer"
              onClick={handleSubmit}
            >
              <TickIcon
                fill="#00d9b1"
                stroke="#00d9b1"
                height={22}
                width={22}
              />
            </div>
          )}
          <div
            className="p-4 bg-primary-light flex items-center justify-center rounded-full cursor-pointer"
            onClick={handleClose}
          >
            <CloseIcon height={22} width={22} />
          </div>
        </div>

        {transcript && (
          <div className="pt-4 text-primary-0 px-6">
            <p>{transcript}</p>
          </div>
        )}

        {error && <p className="text-red-500 text-sm mt-2">Error: {error}</p>}
      </div>
    </div>
  );
};

export default Recorder;
