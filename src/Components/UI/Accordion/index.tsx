import { CaretRightIcon } from '@Icons';
import clsx from 'clsx';

interface AccordionType {
  title: string;
  children?: React.ReactNode;
  setIsOpen?: (open: boolean) => null;
  isOpen: boolean;
  onClickRight?: () => void;
  showIcon?: boolean;
}
const Accordion = ({
  title,
  children,
  isOpen,
  setIsOpen,
  onClickRight,
  showIcon = true,
}: AccordionType): JSX.Element => {
  return (
    <div
      className="border border-[#F1F1F1] shadow-md rounded-[10px] overflow-hidden"
      onClick={onClickRight}
    >
      {/* Accordion Header */}
      <div
        className="h-[64px] flex items-center justify-between p-6 cursor-pointer bg-white sticky top-0"
        onClick={() => (setIsOpen ? setIsOpen(!isOpen) : null)}
      >
        <div className="text-lg font-medium text-black truncate w-[35rem]">
          {title}
        </div>
        {showIcon && (
          <CaretRightIcon
            height={24}
            width={24}
            onClick={onClickRight}
            fill={isOpen ? '#00D9B1' : '#666666'}
            className={clsx(
              'transition-transform duration-300',
              isOpen ? 'rotate-[268deg] mb-[1rem]' : 'rotate-0 mt-2'
            )}
          />
        )}
      </div>

      {/* Accordion Content */}
      <div
        className={clsx(
          'pt-0 grid transition-[max-height,opacity] duration-500 ease-in-out overflow-hidden',
          isOpen
            ? 'max-h-[70vh] opacity-100 p-6 overflow-y-scroll custom-scrollbar'
            : 'max-h-0 opacity-0 p-0'
        )}
      >
        {isOpen && <div className="flex flex-col gap-4">{children}</div>}
      </div>
    </div>
  );
};
export default Accordion;
