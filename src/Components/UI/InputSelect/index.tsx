/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import Select, { components, OptionProps } from 'react-select';

// Define types for the CustomOption component props
interface CustomOptionProps extends OptionProps {
  isSelected: boolean;
}

// Custom Option component to add a tick mark for the selected option
const CustomOption: React.FC<CustomOptionProps> = (props) => {
  return (
    <components.Option {...props}>
      <div className="flex items-center justify-between">
        <span>{props.label}</span>
        {/* {props.isSelected && <CircleTickIcon height={20} width={20} />} */}
      </div>
    </components.Option>
  );
};

// Define the type for the props of InputSelect component
interface InputSelectProps {
  options: Array<{
    label: string;
    value: string;
    prefixes?: Array<{ label: string; value: string }>;
  }>;
  label: string | boolean;
  field?: { onBlur: () => void; value: any; onChange: (val: any) => void };
  errorMessage?: string;
  disabled?: boolean;
  smallDropdown?: boolean;
  [key: string]: any; // To allow for additional props
  onChange?: (val: any) => void;
  placeholder?: string;
}

const InputSelect: React.FC<InputSelectProps> = ({
  options,
  label,
  field,
  errorMessage,
  disabled = false,
  smallDropdown = false,
  onChange,
  placeholder,
  ...rest
}) => {
  const customStyles = {
    control: (provided: any, state: any) => ({
      ...provided,
      borderRadius: '10px',
      paddingBlock: '6px',
      paddingLeft: '4px',
      cursor: disabled ? 'not-allowed' : 'pointer',
      backgroundColor: disabled ? '#e5e7eb' : '#fff',
      borderColor:
        state.isFocused && !errorMessage
          ? '#00D9B1'
          : errorMessage
            ? '#ef4444'
            : '#E0DEF7',

      boxShadow: state.isFocused ? '0px 0px 14px rgba(0,0,0,0.15)' : 'none',

      '&:hover': {
        borderColor: errorMessage ? '#ef4444' : '#00D9B1',
        backgroundColor: disabled ? '#e5e7eb' : '#fff',
        cursor: disabled ? 'not-allowed' : 'pointer',
      },
    }),
    singleValue: (provided: any) => ({
      ...provided,
      color: disabled ? 'grey' : '#000',
    }),
    option: (provided: any, state: any) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? '#CCF7EF'
        : state.isFocused
          ? '#f3f4f6'
          : null,
      color: state.isSelected ? '#00D9B1' : '#000',
      '&:hover': {
        backgroundColor: '#f3f4f6',
      },
    }),
    indicatorSeparator: () => ({
      display: 'none', // Remove separator
    }),
    dropdownIndicator: (provided: any) => ({
      ...provided,
      padding: '0px 4px 0px 1px', // Remove left padding from arrow icon
    }),
    menuList: (provided: any) => ({
      ...provided,
      maxHeight: smallDropdown ? '70px' : '200px', // Adjust height if needed
      overflowY: 'auto', // Keep scrollbar visible
      scrollbarWidth: 'thin', // Standard property for Firefox
      '&::-webkit-scrollbar': {
        width: '6px', // Reduce scrollbar width for Chrome, Safari
      },
      '&::-webkit-scrollbar-thumb': {
        background: '#ccc', // Scroll thumb color
        borderRadius: '10px',
      },
      '&::-webkit-scrollbar-track': {
        background: 'transparent', // Hide scrollbar track
      },
    }),
  };

  return (
    <div className="flex flex-col gap-1.5 w-full pt-0 select-none">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <Select
        options={options}
        placeholder={placeholder ?? 'Select...'}
        styles={customStyles}
        className="w-full"
        classNamePrefix="react-select"
        components={{ Option: CustomOption }} // Use the custom Option component
        isDisabled={disabled}
        {...field}
        {...rest}
        onChange={(val) => {
          if (onChange) {
            onChange(val);
          } else {
            field?.onChange(val);
          }
        }}
      />
      {errorMessage && (
        <p className="text-red-500 text-sm break-words">{errorMessage}</p>
      )}
    </div>
  );
};

export default InputSelect;
