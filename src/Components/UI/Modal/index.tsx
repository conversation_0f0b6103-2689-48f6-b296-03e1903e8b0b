import React, { ReactNode } from 'react';
import clsx from 'clsx';
import { CloseIcon } from '@Icons';

interface ModalProps {
  isOpen: boolean;
  onClose?: () => void;
  children: ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'xxxl' | 'none';
  header?: string | ReactNode;
  headerWrapperClassName?: string;
  hideCloseButton?: boolean;
  outsideClickClose?: boolean;
}

const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose = () => {},
  children,
  className = '',
  size = 'md',
  header = true,
  headerWrapperClassName,
  hideCloseButton = false,
  outsideClickClose = true,
}) => {
  if (!isOpen) return null;

  const sizeClasses: Record<
    'sm' | 'md' | 'lg' | 'xl' | 'xxl' | 'xxxl' | 'none',
    string
  > = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    xxl: 'max-w-2xl',
    xxxl: 'max-w-6xl',
    none: '',
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden outline-none focus:outline-none max-h-screen">
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={() => {
          if (outsideClickClose) {
            onClose();
          }
        }}
      ></div>

      {/* Modal Container */}
      <div
        className={clsx(
          'relative w-full mx-auto flex flex-col overflow-hidden bg-white rounded-lg shadow-xl transform transition-all duration-300 ease-in-out p-6 max-h-[95vh]',
          sizeClasses[size],
          className
        )}
      >
        {/* Modal Header */}
        {header && (
          <div
            className={clsx(
              'flex items-center justify-between pb-4',
              headerWrapperClassName
            )}
          >
            <h3 className="text-lg font-semibold text-gray-900">{header}</h3>
            {!hideCloseButton && onClose && (
              <button
                type="button"
                className="text-gray-400 bg-transparent hover:scale-110 cursor-pointer rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
                onClick={onClose}
              >
                <CloseIcon height={14} width={14} stroke="currentColor" />
                <span className="sr-only">Close modal</span>
              </button>
            )}
          </div>
        )}

        {/* Modal Body */}
        <div className="relative overflow-auto custom-scrollbar">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
