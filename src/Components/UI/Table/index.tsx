import { forwardRef } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  getSortedRowModel,
  ColumnDef,
  RowData,
  SortingState,
} from '@tanstack/react-table';

import clsx from 'clsx';
import { AscSortIcon, DscSortIcon, Loader, NoSortIcon } from '@Icons';
import Pagination from '../Pagination';
import FilterPopover from './FilterPopover';

// Define types for the props
type FilterDataProps = {
  label: string;
  value: string;
};

interface DataTableProps<TData extends RowData> {
  sorting?: SortingState;
  setSorting?: (sorting: SortingState) => void;
  data: TData[];
  columns: ColumnDef<TData>[];
  loading?: boolean;
  isClientSideSorting?: boolean;
  loaderSpan?: number;
  totalPages?: number;
  currentPage?: number;
  onPageChange?: (page: number) => void;
  isInfinite?: boolean;
  pageSize?: number;
  setPageSize?: (pageSize: number) => void;
  pageSizeOptions?: number[];
  onPageSizeChange?: (pageSize: number) => void;
  isPagination?: boolean;
  customHeight?: string;
  filterData?: FilterDataProps[];
  onFilterChange?: (columnId: string, value: string) => void;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const DataTable = forwardRef<HTMLDivElement, DataTableProps<any>>(
  function DataTable(
    {
      sorting = [],
      setSorting = () => {},
      data = [],
      columns = [],
      loading = false,
      isClientSideSorting = false,
      loaderSpan = columns.length,
      totalPages = 0,
      currentPage = 0,
      onPageChange = () => {},
      isInfinite = false,
      pageSize = 10,
      setPageSize = () => {},
      pageSizeOptions = [10, 20, 30, 50, 100],
      onPageSizeChange = () => {},
      isPagination = false,
      customHeight = '',
      filterData = [],
      onFilterChange,
    },
    ref
  ) {
    const table = useReactTable({
      data,
      columns: columns.map((col) => ({
        enableColumnFilter: false,
        ...col,
      })),
      state: {
        sorting,
      },
      onSortingChange: (
        updater: SortingState | ((old: SortingState) => SortingState)
      ) => {
        // Ensure we correctly update the sorting state
        setSorting(typeof updater === 'function' ? updater(sorting) : updater);
      },
      manualSorting: !isClientSideSorting,
      getCoreRowModel: getCoreRowModel(),
      ...(isClientSideSorting
        ? { getSortedRowModel: getSortedRowModel() }
        : {}),
    });

    return (
      <>
        <div
          ref={ref}
          className={clsx(
            'overflow-x-auto custom-scrollbar',
            isInfinite
              ? 'max-h-[calc(100%-3px)]'
              : loading || !totalPages || !isPagination
                ? 'max-h-auto h-full'
                : customHeight
                  ? customHeight
                  : 'max-h-[calc(100%-72px)]' // Height of Pagination section
          )}
        >
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header, index) => {
                    const isFirstColumn = index === 0;
                    const isLastColumn =
                      index === headerGroup.headers.length - 1;
                    return (
                      <th
                        key={header.id}
                        className={`bg-[#D9FFF8] sticky top-0 py-5 font-inter text-sm font-bold leading-4 text-left text-[#475467] tracking-wider ${
                          header.column.getCanSort()
                            ? 'cursor-pointer select-none'
                            : ''
                        } ${isFirstColumn ? 'rounded-tl-md px-6' : 'pl-4'} ${isLastColumn ? 'rounded-tr-md' : ''}`}
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        <div className="flex gap-5">
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {header.column.getCanSort() && (
                            <span className="ml-2 transition-colors">
                              {{
                                asc: (
                                  <AscSortIcon
                                    height={14}
                                    width={14}
                                    fill={
                                      header.column.getIsSorted()
                                        ? '#00d9b1'
                                        : '#9CA3AF'
                                    } // Blue if active, Gray otherwise
                                  />
                                ),
                                desc: (
                                  <DscSortIcon
                                    height={14}
                                    width={14}
                                    fill={
                                      header.column.getIsSorted()
                                        ? '#00d9b1'
                                        : '#9CA3AF'
                                    }
                                  />
                                ),
                              }[
                                header.column.getIsSorted() as 'asc' | 'desc'
                              ] ?? (
                                <NoSortIcon
                                  height={14}
                                  width={14}
                                  fill="#9CA3AF"
                                /> // Default gray for unsorted
                              )}
                            </span>
                          )}
                          {header.column.getCanFilter() ? (
                            <FilterPopover
                              column={header.column}
                              options={filterData}
                              onFilterChange={(value) =>
                                onFilterChange?.(header.column.id, value)
                              }
                            />
                          ) : // <SelectFilter column={header.column} />
                          null}
                        </div>
                      </th>
                    );
                  })}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td
                    colSpan={loaderSpan}
                    className="font-inter text-sm font-semibold leading-4 text-[#475467] text-center p-20"
                  >
                    <Loader height={30} width={30} fill="#D0D5DD" />
                  </td>
                </tr>
              ) : data.length ? (
                table.getRowModel().rows.map((row, index) => {
                  const isLastRow =
                    index === table.getRowModel().rows.length - 1;
                  return (
                    <tr key={row.id} className="hover:bg-gray-50">
                      {row.getVisibleCells().map((cell, index) => {
                        const isFirstColumn = index === 0;
                        const isLastColumn =
                          index === row.getVisibleCells().length - 1;

                        return (
                          <td
                            key={cell.id}
                            className={`max-w-[500px] overflow-hidden text-ellipsis py-4 whitespace-nowrap font-inter text-sm font-normal leading-5 text-left text-[#475467] ${index === 0 ? 'px-6' : 'px-4'} ${isLastRow && isFirstColumn ? 'rounded-bl-lg' : ''} ${isLastRow && isLastColumn ? 'rounded-br-lg' : ''}`}
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td
                    colSpan={loaderSpan}
                    className="font-inter text-lg leading-4 text-[#475467] text-center p-20"
                  >
                    No data Found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>

        {totalPages && isPagination ? (
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            onPageChange={onPageChange}
            isLoading={loading}
            pageSize={pageSize}
            setPageSize={setPageSize}
            options={pageSizeOptions}
            disabled={loading || !data.length}
            onPageSizeChange={onPageSizeChange}
          />
        ) : null}
      </>
    );
  }
);

export default DataTable;
