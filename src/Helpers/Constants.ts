import { PATHS } from '@Config/Path.Config';
export const API_PATHS = {
  //COUNTS
  EXAMPLE: 'posts',
  LOGIN: 'users/auth/login/',
  VERIFY_OTP: 'users/auth/verify-otp/',
  RESEND_OTP: 'users/auth/resend-otp/',
  REGISTER: 'users/auth/signup/',
  LOGOUT: 'users/auth/logout/',
  DELETE_ACCOUNT: 'users/delete/',
  USER_PROFILE: 'users/profile/',
  USER_LICENSE_UPDATE: 'users/license/update/',
  NEW_REFRESH_TOKEN: 'cms/auth/refresh-token/',
  COUNTRIES_LIST: 'address/countries/',
  STATES_LIST: 'address/countries/',
  FOEMAT_STATE_LIST: 'address/state/formats/',
  ABOUT_US: 'users/static-page/about-us/',
  TERMS_AND_CONDITION: 'users/static-page/terms-conditions/',
  PRIVACY_POLICY: 'users/static-page/privacy-policy/',
  DISCLAIMER: 'users/static-page/disclaimer/',
  CONTACT_US: 'users/contact-us/',
  NOTIFICATION_SETTINGS: 'users/notifications/preferences/',
  CHAT_SESSION_HISTORY: 'rag/chat-history',
  CHAT_SESSION_DETAILS: 'rag/chat-detail',
  RECENT_SEARCH: 'rag/recent-five-searches',
  DELETE_CHAT_SESSION: 'rag/chat-sessions/delete/',
  DOC_SESSION_HISTORY: 'rag/document-history',
  DOC_SESSION_DETAILS: 'rag/document-detail',
  DELETE_DOC_SESSION: 'rag/document-sessions/delete/',
  USER_EDIT_PROFILE: 'users/edit-profile/',
  GET_SUBSCRIPTION_PLAN: 'payment/product/list/',
  CREATE_STRIPE_SESSION: 'payment/create-stripe-session/',
  CANCEL_SUBSCRIPTION: 'payment/cancel-subscription/',
  FEATURE_LIST: 'cms/features/',
  VIDEO_LISTINGS: 'training-education/category-wise-videos/',
  VIDEO_DETAILS: 'training-education/video-detail/',
  VIDEO_PURCHASE: 'training-education/purchase-video/',
  QUIZ_LISTING: 'training-education/quiz-listing/',
  QUIZ_ANSWERS: 'training-education/answers-listing/',
  QUIZ_SUBMIT: 'training-education/quiz/submit/',
  PURCHASE_UPLOAD_REPORT: 'payment/create-reports-session/',
  // ENTERPRISES
  ENTERPRISE_REGISTER: 'organization/register/',
  ENTERPRISE_REQUESTS: 'cms/organization/requests/',
  ENTERPRISE_REQUEST_APPROVE: 'cms/organization/request/approve/',
  ENTERPRISE_REQUEST_REJECT: 'cms/organization/request/reject/',
  ENTERPRISE_LIST: 'cms/organization/list/',
  ENTERPRISE_DELETE: 'cms/organization/delete/',
  ENTERPRISE_EDIT: 'organization/update/',
  ENTERPRISE_DETAILS: 'organization/details/',

  ENTERPRISE_MEMBER_ADD: 'organization/member/add/',
  ENTERPRISE_MEMBER_TOGGLE: 'organization/member/toggle-status/',
  ENTERPRISE_MEMBERS_LIST: 'organization/member/list/',
  ENTERPRISE_MEMBERS_EDIT: 'organization/member/edit/',
  ENTERPRISE_MEMBERS_DELETE: 'organization/member/delete/',
  ENTERPRISE_DOWNLOAD_SAMPLE_CSV:
    'organization/member/download-sample-csv/web/',
  ENTERPRISE_MEMBER_UPLOAD_CSV: 'organization/member/upload-csv/',

  GET_FORMS_AND_DOCS_LIST: 'form-document/forms-list/1/',
  GET_FORM_UPDATE_LOGO: 'form-document/logo/',

  //BUILDERR"S HUB
  GET_POST_LIST: 'builder-hub/posts/',
  GET_COMMENT_LIST: 'builder-hub/comments/',
  GET_BH_CATEGORY_LIST: 'builder-hub/categories/',
  POST_COMMENT: 'builder-hub/comments/',
  UPDATE_COMMENT: 'builder-hub/comments/',
  POST_REPLY: 'builder-hub/reply/',
  ADD_POST: 'builder-hub/posts/',
  DELETE_POST: 'builder-hub/posts/',
  GET_REPLIES: 'builder-hub/reply/',
  UPDATE_REPLY: 'builder-hub/reply/',
  DELETE_COMMENT: 'builder-hub/comments/',
  DELETE_REPLY: 'builder-hub/reply/',
  GET_REPORT_REASON_LIST: 'builder-hub/report/reasons/',
  CREATE_REPORT: 'builder-hub/report/',
  GET_POST_DETAILS: 'builder-hub/post/detail/',
  CODE_OF_CONDUCT: 'users/static-page/code-of-conduct/',
  ACCEPT_CODE_OF_CONDUCT: 'users/code-of-conduct-update/',
  LEGEL_CODE_OF_CONDUCT: 'users/get-legal-disclaimer/',
  LEGEL_ACCEPT_CODE_OF_CONDUCT: 'users/accept-legal-disclaimer/',

  //NOTIFICATION_SETTINGS
  NOTIFICATION_LIST: 'users/notifications/',
  NOTIFICATION_READ_ALL: 'users/notifications/mark-all-read/',
  NOTIFICATION_DELETE_ALL: 'users/notifications/delete-all/',
  NOTIFICATION: 'users/notifications/',

  //Analytic Data
  GET_UPLOAD_COUNT: 'analytics-data/get-document-upload-count/?time_range=',
  GET_CATEGORY_WISE_QUESTION_COUNT:
    'analytics-data/get-category-wise-questions-count/?time_range=',
  GET_CATEGORY_WISE_ISSUES_COUNT:
    'analytics-data/get-category-wise-issues-count/?time_range=',

  GET_RF_WISE_ISSUES_COUNT:
    'analytics-data/get-rectification-wise-issues-count/?time_range=',

  // Get data subscription wise
  GET_MEMBER_LIST: 'payment/sub-users/list/',
  PURCHASE_USER: 'payment/purchase-user-session/',
  SUB_USER_ADD: 'payment/sub-users/add/',
  EDIT_USER_ADD: 'payment/sub-users/edit/',
  DELETE_USER_ADD: 'payment/sub-users/delete/',
  PURCHASE_SUB_USER: 'payment/purchase-user-session/',
  CHECK_INVOICE_STATUS: 'payment/check-invoice-status/',
};

export const MODULE_KEY = {
  ADMINS: 'admin-management',
  KNOWLEDGEBASE: 'knowledge-base',
  USERS: 'user-management',
  CONTACT_REQUEST: 'contact-request',
  LICENCE_APPROVAL: 'licence-approval',
  STATIC_PAGES: 'static-pages',
  ENTERPRISE: 'enterprise-management',
  SUBSCRIPTION: 'subscription-management',
  NOTIFICATION: 'notification-templates',
  FORMS_AND_DOCUMENTS: 'forms-and-documents',
  BUILDERS_HUB: 'builders-hub',
  TRAINING_EDUCATION: 'training-education',
};

export const USER_ROLES = {
  ORGANIZATION_ADMIN: 'org-admin',
  ORGANIZATION_USER: 'org-user',
  USER: 'user',
};

export const MODULES_PATHS: Record<string, string> = {
  [PATHS.FORMSANDDOC]: 'forms-and-documents',
  [PATHS.DETAILOFDOC]: 'forms-and-documents',
  [PATHS.HISTORY]: 'histroy',
  [PATHS.HOME]: 'home',
  [PATHS.LEGALS]: 'legals',
  [PATHS.BUILDER_HUB]: 'builders-hub',
  [PATHS.TRAINING]: 'training-education',
  [PATHS.ANALYTICS]: 'analytic-data',
};

export const PURCHASE_UPLOAD_REPORT_PRODUCT_ID =
  'price_1RQqu54CSmcGXVIpkZXxFfUx';
