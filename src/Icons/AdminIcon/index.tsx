import React from 'react';

const AdminIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 28 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M26.8648 23.5C24.9611 20.2087 22.0273 17.8487 18.6036 16.73C20.2971 15.7218 21.6129 14.1856 22.3489 12.3572C23.0848 10.5289 23.2002 8.50948 22.6774 6.60918C22.1546 4.70887 21.0224 3.03272 19.4548 1.83814C17.8872 0.643552 15.9708 -0.00341797 13.9999 -0.00341797C12.0289 -0.00341797 10.1125 0.643552 8.54488 1.83814C6.97725 3.03272 5.84509 4.70887 5.32228 6.60918C4.79946 8.50948 4.91489 10.5289 5.65083 12.3572C6.38678 14.1856 7.70256 15.7218 9.3961 16.73C5.97235 17.8475 3.0386 20.2075 1.13485 23.5C1.06504 23.6138 1.01873 23.7405 0.998661 23.8725C0.978593 24.0045 0.985171 24.1392 1.01801 24.2687C1.05084 24.3981 1.10927 24.5197 1.18984 24.6262C1.27041 24.7326 1.3715 24.8219 1.48713 24.8887C1.60277 24.9555 1.73061 24.9985 1.86311 25.015C1.99561 25.0316 2.1301 25.0215 2.25863 24.9853C2.38716 24.949 2.50713 24.8874 2.61146 24.8041C2.71579 24.7207 2.80238 24.6173 2.8661 24.5C5.2211 20.43 9.3836 18 13.9999 18C18.6161 18 22.7786 20.43 25.1336 24.5C25.1973 24.6173 25.2839 24.7207 25.3882 24.8041C25.4926 24.8874 25.6125 24.949 25.7411 24.9853C25.8696 25.0215 26.0041 25.0316 26.1366 25.015C26.2691 24.9985 26.3969 24.9555 26.5126 24.8887C26.6282 24.8219 26.7293 24.7326 26.8099 24.6262C26.8904 24.5197 26.9489 24.3981 26.9817 24.2687C27.0145 24.1392 27.0211 24.0045 27.001 23.8725C26.981 23.7405 26.9347 23.6138 26.8648 23.5ZM6.99985 8.99998C6.99985 7.61551 7.41039 6.26214 8.17956 5.11099C8.94873 3.95985 10.042 3.06264 11.3211 2.53283C12.6001 2.00301 14.0076 1.86439 15.3655 2.13449C16.7234 2.40458 17.9706 3.07127 18.9496 4.05023C19.9286 5.0292 20.5953 6.27648 20.8653 7.63435C21.1354 8.99222 20.9968 10.3997 20.467 11.6788C19.9372 12.9578 19.04 14.0511 17.8888 14.8203C16.7377 15.5894 15.3843 16 13.9999 16C12.1439 15.998 10.3646 15.2599 9.0523 13.9475C7.73997 12.6352 7.00184 10.8559 6.99985 8.99998Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default AdminIcon;
