import React from 'react';

const AscIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 20 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M21.0612 12.0613C20.9218 12.2011 20.7563 12.3121 20.5739 12.3878C20.3916 12.4635 20.1961 12.5025 19.9987 12.5025C19.8013 12.5025 19.6058 12.4635 19.4235 12.3878C19.2411 12.3121 19.0756 12.2011 18.9362 12.0613L12.4999 5.62505V24C12.4999 24.3979 12.3419 24.7794 12.0606 25.0607C11.7793 25.342 11.3978 25.5 10.9999 25.5C10.6021 25.5 10.2206 25.342 9.93929 25.0607C9.65798 24.7794 9.49995 24.3979 9.49995 24V5.62505L3.0612 12.0613C2.7794 12.3431 2.39721 12.5014 1.9987 12.5014C1.60018 12.5014 1.21799 12.3431 0.936196 12.0613C0.654403 11.7795 0.496094 11.3973 0.496094 10.9988C0.496094 10.6003 0.654403 10.2181 0.936196 9.9363L9.9362 0.9363C10.0756 0.79646 10.2411 0.685505 10.4235 0.609797C10.6058 0.534089 10.8013 0.495117 10.9987 0.495117C11.1961 0.495117 11.3916 0.534089 11.5739 0.609797C11.7563 0.685505 11.9218 0.79646 12.0612 0.9363L21.0612 9.9363C21.201 10.0757 21.312 10.2412 21.3877 10.4236C21.4634 10.6059 21.5024 10.8014 21.5024 10.9988C21.5024 11.1962 21.4634 11.3917 21.3877 11.574C21.312 11.7564 21.201 11.9219 21.0612 12.0613Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default AscIcon;
