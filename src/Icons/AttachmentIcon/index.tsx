import React from 'react';

const AttachmentIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 20 20" fill="none">
      <path
        d="M13.5306 6.46878C13.6004 6.53843 13.6557 6.62115 13.6934 6.7122C13.7312 6.80325 13.7506 6.90084 13.7506 6.9994C13.7506 7.09796 13.7312 7.19556 13.6934 7.28661C13.6557 7.37766 13.6004 7.46037 13.5306 7.53003L7.53065 13.53C7.46097 13.5997 7.37824 13.655 7.2872 13.6927C7.19615 13.7304 7.09857 13.7498 7.00002 13.7498C6.90148 13.7498 6.8039 13.7304 6.71285 13.6927C6.62181 13.655 6.53908 13.5997 6.4694 13.53C6.39972 13.4603 6.34444 13.3776 6.30673 13.2866C6.26902 13.1955 6.24961 13.0979 6.24961 12.9994C6.24961 12.9009 6.26902 12.8033 6.30673 12.7122C6.34444 12.6212 6.39972 12.5385 6.4694 12.4688L12.4694 6.46878C12.5391 6.39904 12.6218 6.34373 12.7128 6.30598C12.8039 6.26824 12.9015 6.24881 13 6.24881C13.0986 6.24881 13.1962 6.26824 13.2872 6.30598C13.3783 6.34373 13.461 6.39904 13.5306 6.46878ZM18.2125 1.7869C17.725 1.29934 17.1462 0.912578 16.5092 0.648707C15.8722 0.384836 15.1895 0.249023 14.5 0.249023C13.8105 0.249023 13.1278 0.384836 12.4908 0.648707C11.8538 0.912578 11.275 1.29934 10.7875 1.7869L7.9694 4.60409C7.82867 4.74482 7.74961 4.93569 7.74961 5.13471C7.74961 5.33374 7.82867 5.52461 7.9694 5.66534C8.11013 5.80607 8.301 5.88513 8.50002 5.88513C8.69905 5.88513 8.88992 5.80607 9.03065 5.66534L11.8488 2.85284C12.5546 2.16251 13.5042 1.77839 14.4915 1.78382C15.4787 1.78926 16.4241 2.18382 17.1222 2.88188C17.8204 3.57994 18.2151 4.52518 18.2208 5.51245C18.2264 6.49973 17.8424 7.44939 17.1522 8.15534L14.3331 10.9735C14.1924 11.1141 14.1133 11.3048 14.1132 11.5038C14.1131 11.7027 14.1921 11.8935 14.3327 12.0342C14.4733 12.175 14.664 12.2541 14.863 12.2542C15.0619 12.2543 15.2527 12.1753 15.3935 12.0347L18.2125 9.2119C18.7001 8.72439 19.0868 8.14561 19.3507 7.50861C19.6146 6.87162 19.7504 6.18889 19.7504 5.4994C19.7504 4.80992 19.6146 4.12718 19.3507 3.49019C19.0868 2.8532 18.7001 2.27442 18.2125 1.7869ZM10.9694 14.3325L8.15127 17.1507C7.80447 17.5052 7.39077 17.7875 6.93413 17.9811C6.47749 18.1747 5.98698 18.2758 5.491 18.2785C4.99502 18.2813 4.50342 18.1856 4.04468 17.997C3.58593 17.8085 3.16915 17.5308 2.81846 17.18C2.46778 16.8293 2.19015 16.4124 2.00167 15.9537C1.81319 15.4949 1.71759 15.0033 1.72041 14.5073C1.72323 14.0113 1.82441 13.5208 2.01809 13.0642C2.21178 12.6076 2.49412 12.194 2.84877 11.8472L5.66596 9.03003C5.80669 8.8893 5.88575 8.69843 5.88575 8.4994C5.88575 8.30038 5.80669 8.10951 5.66596 7.96878C5.52523 7.82805 5.33436 7.74898 5.13534 7.74898C4.93631 7.74898 4.74544 7.82805 4.60471 7.96878L1.78752 10.7869C0.802908 11.7715 0.249756 13.1069 0.249756 14.4994C0.249756 15.8919 0.802908 17.2273 1.78752 18.2119C2.77214 19.1965 4.10757 19.7497 5.50002 19.7497C6.89248 19.7497 8.22791 19.1965 9.21252 18.2119L12.0306 15.3928C12.1713 15.2521 12.2502 15.0613 12.2501 14.8624C12.25 14.6634 12.1709 14.4727 12.0302 14.3321C11.8894 14.1915 11.6986 14.1125 11.4997 14.1126C11.3008 14.1127 11.11 14.1918 10.9694 14.3325Z"
        fill="url(#paint0_linear_1675_2032)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1675_2032"
          x1="10.0001"
          y1="0.249023"
          x2="10.0001"
          y2="19.7497"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default AttachmentIcon;
