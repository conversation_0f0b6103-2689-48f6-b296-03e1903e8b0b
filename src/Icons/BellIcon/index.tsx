import React from 'react';

const BellIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 24 27" fill="none">
      <path
        d="M23.725 19.4925C23.0313 18.2975 22 14.9163 22 10.5C22 7.84784 20.9464 5.3043 19.0711 3.42893C17.1957 1.55357 14.6522 0.5 12 0.5C9.34785 0.5 6.80431 1.55357 4.92895 3.42893C3.05358 5.3043 2.00002 7.84784 2.00002 10.5C2.00002 14.9175 0.967516 18.2975 0.273766 19.4925C0.0966042 19.7963 0.00268396 20.1415 0.00147663 20.4931C0.000269303 20.8448 0.0918178 21.1906 0.266889 21.4956C0.441961 21.8006 0.694365 22.0541 0.998648 22.2304C1.30293 22.4068 1.64833 22.4997 2.00002 22.5H7.10127C7.33198 23.6289 7.94555 24.6436 8.83818 25.3722C9.73082 26.1009 10.8477 26.4989 12 26.4989C13.1523 26.4989 14.2692 26.1009 15.1618 25.3722C16.0545 24.6436 16.6681 23.6289 16.8988 22.5H22C22.3516 22.4995 22.6968 22.4064 23.0009 22.23C23.3051 22.0535 23.5573 21.8 23.7322 21.4951C23.9071 21.1901 23.9986 20.8444 23.9973 20.4928C23.996 20.1412 23.9021 19.7962 23.725 19.4925ZM12 24.5C11.3798 24.4998 10.7749 24.3074 10.2685 23.9492C9.76216 23.5911 9.37926 23.0848 9.17252 22.5H14.8275C14.6208 23.0848 14.2379 23.5911 13.7315 23.9492C13.2252 24.3074 12.6202 24.4998 12 24.5ZM2.00002 20.5C2.96252 18.845 4.00002 15.01 4.00002 10.5C4.00002 8.37827 4.84287 6.34344 6.34316 4.84315C7.84345 3.34285 9.87828 2.5 12 2.5C14.1217 2.5 16.1566 3.34285 17.6569 4.84315C19.1572 6.34344 20 8.37827 20 10.5C20 15.0063 21.035 18.8412 22 20.5H2.00002Z"
        fill="url(#paint0_linear_1462_528)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_528"
          x1="11.9994"
          y1="0.5"
          x2="11.9994"
          y2="26.4989"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default BellIcon;
