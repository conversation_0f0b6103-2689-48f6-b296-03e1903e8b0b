import React from 'react';

const ChartLineUpIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 26 23" fill="none">
      <path
        d="M26 21.5C26 21.7652 25.8946 22.0196 25.7071 22.2071C25.5196 22.3946 25.2652 22.5 25 22.5H1C0.734784 22.5 0.48043 22.3946 0.292893 22.2071C0.105357 22.0196 0 21.7652 0 21.5V1.5C0 1.23478 0.105357 0.98043 0.292893 0.792893C0.48043 0.605357 0.734784 0.5 1 0.5C1.26522 0.5 1.51957 0.605357 1.70711 0.792893C1.89464 0.98043 2 1.23478 2 1.5V15.0863L8.2925 8.7925C8.38537 8.69952 8.49566 8.62576 8.61706 8.57544C8.73846 8.52512 8.86858 8.49921 9 8.49921C9.13142 8.49921 9.26154 8.52512 9.38294 8.57544C9.50434 8.62576 9.61463 8.69952 9.7075 8.7925L13 12.0863L19.5863 5.5H17C16.7348 5.5 16.4804 5.39464 16.2929 5.20711C16.1054 5.01957 16 4.76522 16 4.5C16 4.23478 16.1054 3.98043 16.2929 3.79289C16.4804 3.60536 16.7348 3.5 17 3.5H22C22.2652 3.5 22.5196 3.60536 22.7071 3.79289C22.8946 3.98043 23 4.23478 23 4.5V9.5C23 9.76522 22.8946 10.0196 22.7071 10.2071C22.5196 10.3946 22.2652 10.5 22 10.5C21.7348 10.5 21.4804 10.3946 21.2929 10.2071C21.1054 10.0196 21 9.76522 21 9.5V6.91375L13.7075 14.2075C13.6146 14.3005 13.5043 14.3742 13.3829 14.4246C13.2615 14.4749 13.1314 14.5008 13 14.5008C12.8686 14.5008 12.7385 14.4749 12.6171 14.4246C12.4957 14.3742 12.3854 14.3005 12.2925 14.2075L9 10.9137L2 17.9137V20.5H25C25.2652 20.5 25.5196 20.6054 25.7071 20.7929C25.8946 20.9804 26 21.2348 26 21.5Z"
        fill={props?.fill ?? 'url(#paint0_linear_1462_499)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_506"
          x1="13"
          y1="0.5"
          x2="13"
          y2="22.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props?.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default ChartLineUpIcon;
