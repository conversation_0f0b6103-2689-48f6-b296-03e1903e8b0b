import React from 'react';

const ClockCounterWiseIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 25 25" fill="none">
      <path
        d="M14 6.50004V11.9338L18.515 14.6425C18.7424 14.7791 18.9063 15.0005 18.9705 15.2579C19.0347 15.5152 18.9941 15.7876 18.8575 16.015C18.7209 16.2425 18.4996 16.4063 18.2422 16.4705C17.9848 16.5348 17.7124 16.4941 17.485 16.3575L12.485 13.3575C12.337 13.2686 12.2146 13.1429 12.1296 12.9927C12.0446 12.8424 11.9999 12.6727 12 12.5V6.50004C12 6.23482 12.1054 5.98047 12.2929 5.79293C12.4804 5.60539 12.7348 5.50004 13 5.50004C13.2652 5.50004 13.5196 5.60539 13.7071 5.79293C13.8946 5.98047 14 6.23482 14 6.50004ZM13 0.500037C11.4225 0.496108 9.85987 0.805079 8.40257 1.40907C6.94527 2.01306 5.62222 2.90008 4.51 4.01879C3.60125 4.93879 2.79375 5.82379 2 6.75004V4.50004C2 4.23482 1.89464 3.98047 1.70711 3.79293C1.51957 3.60539 1.26522 3.50004 1 3.50004C0.734784 3.50004 0.48043 3.60539 0.292893 3.79293C0.105357 3.98047 0 4.23482 0 4.50004V9.50004C0 9.76525 0.105357 10.0196 0.292893 10.2071C0.48043 10.3947 0.734784 10.5 1 10.5H6C6.26522 10.5 6.51957 10.3947 6.70711 10.2071C6.89464 10.0196 7 9.76525 7 9.50004C7 9.23482 6.89464 8.98047 6.70711 8.79293C6.51957 8.60539 6.26522 8.50004 6 8.50004H3.125C4.01875 7.44754 4.90875 6.45629 5.92375 5.42879C7.31357 4.03898 9.08213 3.08955 11.0085 2.69913C12.9348 2.30872 14.9335 2.49463 16.7547 3.23364C18.576 3.97266 20.1391 5.23199 21.2487 6.8543C22.3584 8.4766 22.9653 10.3899 22.9938 12.3552C23.0222 14.3205 22.4708 16.2506 21.4086 17.9043C20.3463 19.5581 18.8203 20.8621 17.0212 21.6535C15.2221 22.4448 13.2296 22.6885 11.2928 22.354C9.35598 22.0194 7.56069 21.1216 6.13125 19.7725C6.03571 19.6823 5.92333 19.6117 5.80052 19.5648C5.6777 19.518 5.54686 19.4958 5.41547 19.4995C5.28407 19.5032 5.15469 19.5328 5.03472 19.5865C4.91475 19.6402 4.80653 19.717 4.71625 19.8125C4.62597 19.9081 4.55538 20.0205 4.50853 20.1433C4.46168 20.2661 4.43948 20.3969 4.44319 20.5283C4.44691 20.6597 4.47647 20.7891 4.53018 20.9091C4.58389 21.029 4.66071 21.1373 4.75625 21.2275C6.18056 22.5716 7.91219 23.5467 9.8 24.0677C11.6878 24.5886 13.6744 24.6396 15.5865 24.2162C17.4986 23.7929 19.278 22.9079 20.7694 21.6387C22.2608 20.3695 23.4189 18.7545 24.1427 16.9348C24.8664 15.1151 25.1338 13.1459 24.9215 11.199C24.7091 9.25218 24.0236 7.3869 22.9246 5.76595C21.8256 4.14501 20.3466 2.81766 18.6166 1.89977C16.8867 0.981868 14.9584 0.501312 13 0.500037Z"
        fill="url(#paint0_linear_1462_485)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_485"
          x1="12.4961"
          y1="0.5"
          x2="12.4961"
          y2="24.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props?.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default ClockCounterWiseIcon;
