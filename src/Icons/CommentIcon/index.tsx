import React from 'react';

const CommentIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 19 20" fill="none">
      <path
        d="M9.375 0.75C6.88943 0.75273 4.50645 1.74133 2.74889 3.49889C0.991327 5.25645 0.00272962 7.63943 0 10.125V18.0309C0.000496137 18.4204 0.155431 18.7938 0.430826 19.0692C0.706222 19.3446 1.0796 19.4995 1.46906 19.5H9.375C11.8614 19.5 14.246 18.5123 16.0041 16.7541C17.7623 14.996 18.75 12.6114 18.75 10.125C18.75 7.6386 17.7623 5.25403 16.0041 3.49587C14.246 1.73772 11.8614 0.75 9.375 0.75ZM9.375 18H1.5V10.125C1.5 8.56747 1.96186 7.04492 2.82718 5.74988C3.69249 4.45485 4.9224 3.44549 6.36137 2.84945C7.80034 2.25341 9.38374 2.09746 10.9113 2.40132C12.4389 2.70517 13.8421 3.4552 14.9435 4.55653C16.0448 5.65787 16.7948 7.06106 17.0987 8.58866C17.4025 10.1163 17.2466 11.6997 16.6506 13.1386C16.0545 14.5776 15.0452 15.8075 13.7501 16.6728C12.4551 17.5381 10.9325 18 9.375 18Z"
        fill="url(#paint0_linear_4141_1019)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4141_1019"
          x1="9.375"
          y1="0.75"
          x2="9.375"
          y2="19.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default CommentIcon;
