import React from 'react';

const DownloadIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 14 15" fill="none">
      <path
        d="M13.75 9.1875V13.125C13.75 13.4234 13.6315 13.7095 13.4205 13.9205C13.2095 14.1315 12.9234 14.25 12.625 14.25H1.375C1.07663 14.25 0.790483 14.1315 0.579505 13.9205C0.368526 13.7095 0.25 13.4234 0.25 13.125V9.1875C0.25 9.03832 0.309263 8.89524 0.414753 8.78975C0.520242 8.68426 0.663316 8.625 0.8125 8.625C0.961684 8.625 1.10476 8.68426 1.21025 8.78975C1.31574 8.89524 1.375 9.03832 1.375 9.1875V13.125H12.625V9.1875C12.625 9.03832 12.6843 8.89524 12.7898 8.78975C12.8952 8.68426 13.0383 8.625 13.1875 8.625C13.3367 8.625 13.4798 8.68426 13.5852 8.78975C13.6907 8.89524 13.75 9.03832 13.75 9.1875ZM6.60203 9.58547C6.65427 9.63777 6.71631 9.67926 6.7846 9.70757C6.85288 9.73587 6.92608 9.75044 7 9.75044C7.07392 9.75044 7.14712 9.73587 7.2154 9.70757C7.28369 9.67926 7.34573 9.63777 7.39797 9.58547L10.2105 6.77297C10.2627 6.72071 10.3042 6.65866 10.3325 6.59038C10.3608 6.5221 10.3753 6.44891 10.3753 6.375C10.3753 6.30109 10.3608 6.2279 10.3325 6.15962C10.3042 6.09134 10.2627 6.02929 10.2105 5.97703C10.1582 5.92477 10.0962 5.88331 10.0279 5.85503C9.9596 5.82674 9.88641 5.81219 9.8125 5.81219C9.73859 5.81219 9.6654 5.82674 9.59712 5.85503C9.52884 5.88331 9.46679 5.92477 9.41453 5.97703L7.5625 7.82977V1.3125C7.5625 1.16332 7.50324 1.02024 7.39775 0.914753C7.29226 0.809263 7.14918 0.75 7 0.75C6.85082 0.75 6.70774 0.809263 6.60225 0.914753C6.49676 1.02024 6.4375 1.16332 6.4375 1.3125V7.82977L4.58547 5.97703C4.47992 5.87148 4.33677 5.81219 4.1875 5.81219C4.03823 5.81219 3.89508 5.87148 3.78953 5.97703C3.68398 6.08258 3.62469 6.22573 3.62469 6.375C3.62469 6.52427 3.68398 6.66742 3.78953 6.77297L6.60203 9.58547Z"
        fill={props?.fill ?? '#00D9B1'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1563_2744"
          x1="7"
          y1="0.75"
          x2="7"
          y2="14.25"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color={props?.fill ?? '#00D9B1'} />
          <stop offset="1" stop-color={props?.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default DownloadIcon;
