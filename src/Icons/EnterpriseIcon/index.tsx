import React from 'react';

const EnterpriseIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 22 27" fill="none">
      <path
        d="M24.96 6.26875L13.96 0.249996C13.6659 0.0882749 13.3357 0.003479 13 0.003479C12.6643 0.003479 12.3341 0.0882749 12.04 0.249996L1.04 6.2725C0.725861 6.44438 0.463629 6.69745 0.28069 7.00528C0.0977502 7.31311 0.000812822 7.66441 0 8.0225V19.9775C0.000812822 20.3356 0.0977502 20.6869 0.28069 20.9947C0.463629 21.3025 0.725861 21.5556 1.04 21.7275L12.04 27.7487C12.3339 27.9111 12.6642 27.9963 13 27.9963C13.3358 27.9963 13.6661 27.9111 13.96 27.7487L24.96 21.7275C25.2741 21.5556 25.5364 21.3025 25.7193 20.9947C25.9023 20.6869 25.9992 20.3356 26 19.9775V8.0225C25.9999 7.66377 25.9032 7.31169 25.7203 7.00314C25.5373 6.69459 25.2747 6.44093 24.96 6.26875ZM18 17V23.2612L14 25.4512V14.5925L24 9.1175V13.1175L18.52 16.1175C18.3618 16.204 18.23 16.3318 18.1384 16.4871C18.0469 16.6425 17.9991 16.8197 18 17ZM7.48 16.125L2 13.125V9.125L12 14.6V25.4587L8 23.2612V17C7.99959 16.821 7.95113 16.6453 7.85966 16.4914C7.76819 16.3375 7.63707 16.2109 7.48 16.125ZM7.3925 5.07375L12.5175 7.88C12.6647 7.96052 12.8297 8.00273 12.9975 8.00273C13.1653 8.00273 13.3303 7.96052 13.4775 7.88L18.6025 5.07375L22.9125 7.4325L13 12.86L3.0825 7.43L7.3925 5.07375ZM13 2L16.525 3.93L13 5.86125L9.475 3.93125L13 2ZM2 15.4025L6 17.5925V22.1662L2 19.9775V15.4025ZM20 22.1662V17.5925L24 15.4025V19.9775L20 22.1662Z"
        fill={props?.fill ?? 'url(#paint0_linear_1462_499)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_499"
          x1="11"
          y1="0.5"
          x2="11"
          y2="26.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props?.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default EnterpriseIcon;
