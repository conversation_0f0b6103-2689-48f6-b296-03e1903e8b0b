import React from 'react';

const HouseIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 24 26" fill="none">
      <path
        d="M23.3538 10.4711L13.3538 1.03608C13.3489 1.03181 13.3443 1.02722 13.34 1.02233C12.9718 0.687494 12.492 0.501953 11.9944 0.501953C11.4967 0.501953 11.0169 0.687494 10.6488 1.02233L10.635 1.03608L0.64625 10.4711C0.442497 10.6584 0.279855 10.8861 0.168614 11.1395C0.057374 11.393 -4.20536e-05 11.6668 2.31098e-08 11.9436V23.4998C2.31098e-08 24.0303 0.210714 24.539 0.585786 24.914C0.960859 25.2891 1.46957 25.4998 2 25.4998H8C8.53043 25.4998 9.03914 25.2891 9.41421 24.914C9.78929 24.539 10 24.0303 10 23.4998V17.4998H14V23.4998C14 24.0303 14.2107 24.539 14.5858 24.914C14.9609 25.2891 15.4696 25.4998 16 25.4998H22C22.5304 25.4998 23.0391 25.2891 23.4142 24.914C23.7893 24.539 24 24.0303 24 23.4998V11.9436C24 11.6668 23.9426 11.393 23.8314 11.1395C23.7201 10.8861 23.5575 10.6584 23.3538 10.4711ZM22 23.4998H16V17.4998C16 16.9694 15.7893 16.4607 15.4142 16.0856C15.0391 15.7105 14.5304 15.4998 14 15.4998H10C9.46957 15.4998 8.96086 15.7105 8.58579 16.0856C8.21071 16.4607 8 16.9694 8 17.4998V23.4998H2V11.9436L2.01375 11.9311L12 2.49983L21.9875 11.9286L22.0012 11.9411L22 23.4998Z"
        fill="url(#paint0_linear_1462_2452)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_2452"
          x1="12"
          y1="0.501953"
          x2="12"
          y2="25.4998"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default HouseIcon;
