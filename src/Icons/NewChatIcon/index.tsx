import React from 'react';

const NewChatIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg viewBox="0 0 26 24" fill="none" {...props}>
      <path
        d="M24 0.25H2C1.53587 0.25 1.09075 0.434375 0.762566 0.762563C0.434377 1.09075 0.250003 1.53587 0.250003 2V22C0.248194 22.3336 0.342627 22.6607 0.521985 22.9421C0.701342 23.2234 0.958017 23.4471 1.26125 23.5863C1.49276 23.6936 1.74481 23.7495 2 23.75C2.41227 23.7502 2.81112 23.6035 3.125 23.3363L3.13625 23.3263L7.15375 19.805C7.19874 19.7689 7.25483 19.7495 7.3125 19.75H24C24.4641 19.75 24.9093 19.5656 25.2374 19.2374C25.5656 18.9092 25.75 18.4641 25.75 18V2C25.75 1.53587 25.5656 1.09075 25.2374 0.762563C24.9093 0.434375 24.4641 0.25 24 0.25ZM24.25 18C24.25 18.0663 24.2237 18.1299 24.1768 18.1768C24.1299 18.2237 24.0663 18.25 24 18.25H7.3125C6.9008 18.2505 6.50245 18.3961 6.1875 18.6612L6.17625 18.6712L2.15625 22.1925C2.11966 22.2218 2.07557 22.2402 2.02901 22.2457C1.98245 22.2511 1.9353 22.2433 1.89294 22.2233C1.85058 22.2032 1.81471 22.1716 1.78944 22.1321C1.76416 22.0927 1.7505 22.0469 1.75 22V2C1.75 1.9337 1.77634 1.87011 1.82323 1.82322C1.87011 1.77634 1.9337 1.75 2 1.75H24C24.0663 1.75 24.1299 1.77634 24.1768 1.82322C24.2237 1.87011 24.25 1.9337 24.25 2V18ZM17.75 8C17.75 8.19891 17.671 8.38968 17.5303 8.53033C17.3897 8.67098 17.1989 8.75 17 8.75H9C8.80109 8.75 8.61033 8.67098 8.46967 8.53033C8.32902 8.38968 8.25 8.19891 8.25 8C8.25 7.80109 8.32902 7.61032 8.46967 7.46967C8.61033 7.32902 8.80109 7.25 9 7.25H17C17.1989 7.25 17.3897 7.32902 17.5303 7.46967C17.671 7.61032 17.75 7.80109 17.75 8ZM17.75 12C17.75 12.1989 17.671 12.3897 17.5303 12.5303C17.3897 12.671 17.1989 12.75 17 12.75H9C8.80109 12.75 8.61033 12.671 8.46967 12.5303C8.32902 12.3897 8.25 12.1989 8.25 12C8.25 11.8011 8.32902 11.6103 8.46967 11.4697C8.61033 11.329 8.80109 11.25 9 11.25H17C17.1989 11.25 17.3897 11.329 17.5303 11.4697C17.671 11.6103 17.75 11.8011 17.75 12Z"
        fill={props?.fill ?? '#00d9b1'}
      />
    </svg>
  );
};

export default NewChatIcon;
