import React from 'react';

const NotepadIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 32 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      width={22}
      height={22}
    >
      <path
        d="M20.75 16C20.75 16.1989 20.671 16.3897 20.5303 16.5303C20.3897 16.671 20.1989 16.75 20 16.75H12C11.8011 16.75 11.6103 16.671 11.4697 16.5303C11.329 16.3897 11.25 16.1989 11.25 16C11.25 15.8011 11.329 15.6103 11.4697 15.4697C11.6103 15.329 11.8011 15.25 12 15.25H20C20.1989 15.25 20.3897 15.329 20.5303 15.4697C20.671 15.6103 20.75 15.8011 20.75 16ZM20 19.25H12C11.8011 19.25 11.6103 19.329 11.4697 19.4697C11.329 19.6103 11.25 19.8011 11.25 20C11.25 20.1989 11.329 20.3897 11.4697 20.5303C11.6103 20.671 11.8011 20.75 12 20.75H20C20.1989 20.75 20.3897 20.671 20.5303 20.5303C20.671 20.3897 20.75 20.1989 20.75 20C20.75 19.8011 20.671 19.6103 20.5303 19.4697C20.3897 19.329 20.1989 19.25 20 19.25ZM26.75 6V25C26.75 25.9946 26.3549 26.9484 25.6517 27.6517C24.9484 28.3549 23.9946 28.75 23 28.75H9C8.00544 28.75 7.05161 28.3549 6.34835 27.6517C5.64509 26.9484 5.25 25.9946 5.25 25V6C5.25 5.53587 5.43437 5.09075 5.76256 4.76256C6.09075 4.43437 6.53587 4.25 7 4.25H9.25V3C9.25 2.80109 9.32902 2.61032 9.46967 2.46967C9.61032 2.32902 9.80109 2.25 10 2.25C10.1989 2.25 10.3897 2.32902 10.5303 2.46967C10.671 2.61032 10.75 2.80109 10.75 3V4.25H15.25V3C15.25 2.80109 15.329 2.61032 15.4697 2.46967C15.6103 2.32902 15.8011 2.25 16 2.25C16.1989 2.25 16.3897 2.32902 16.5303 2.46967C16.671 2.61032 16.75 2.80109 16.75 3V4.25H21.25V3C21.25 2.80109 21.329 2.61032 21.4697 2.46967C21.6103 2.32902 21.8011 2.25 22 2.25C22.1989 2.25 22.3897 2.32902 22.5303 2.46967C22.671 2.61032 22.75 2.80109 22.75 3V4.25H25C25.4641 4.25 25.9092 4.43437 26.2374 4.76256C26.5656 5.09075 26.75 5.53587 26.75 6ZM25.25 6C25.25 5.9337 25.2237 5.87011 25.1768 5.82322C25.1299 5.77634 25.0663 5.75 25 5.75H22.75V7C22.75 7.19891 22.671 7.38968 22.5303 7.53033C22.3897 7.67098 22.1989 7.75 22 7.75C21.8011 7.75 21.6103 7.67098 21.4697 7.53033C21.329 7.38968 21.25 7.19891 21.25 7V5.75H16.75V7C16.75 7.19891 16.671 7.38968 16.5303 7.53033C16.3897 7.67098 16.1989 7.75 16 7.75C15.8011 7.75 15.6103 7.67098 15.4697 7.53033C15.329 7.38968 15.25 7.19891 15.25 7V5.75H10.75V7C10.75 7.19891 10.671 7.38968 10.5303 7.53033C10.3897 7.67098 10.1989 7.75 10 7.75C9.80109 7.75 9.61032 7.67098 9.46967 7.53033C9.32902 7.38968 9.25 7.19891 9.25 7V5.75H7C6.9337 5.75 6.87011 5.77634 6.82322 5.82322C6.77634 5.87011 6.75 5.9337 6.75 6V25C6.75 25.5967 6.98705 26.169 7.40901 26.591C7.83097 27.0129 8.40326 27.25 9 27.25H23C23.5967 27.25 24.169 27.0129 24.591 26.591C25.0129 26.169 25.25 25.5967 25.25 25V6Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default NotepadIcon;
