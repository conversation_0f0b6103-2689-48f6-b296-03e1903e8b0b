import React from 'react';

const NotepadIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 32 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      width={22}
      height={22}
    >
      <path
        d="M19.275 4.80626C19.3004 4.71102 19.3443 4.62172 19.4042 4.54347C19.4642 4.46521 19.5389 4.39954 19.6243 4.3502C19.7096 4.30086 19.8038 4.26882 19.9015 4.25592C19.9992 4.24302 20.0985 4.24951 20.1937 4.27501C22.0052 4.74753 23.658 5.69446 24.9818 7.01824C26.3055 8.34201 27.2525 9.99477 27.725 11.8063C27.7505 11.9015 27.757 12.0008 27.7441 12.0985C27.7312 12.1962 27.6992 12.2904 27.6498 12.3758C27.6005 12.4611 27.5348 12.5358 27.4565 12.5958C27.3783 12.6557 27.289 12.6996 27.1937 12.725C27.1305 12.7418 27.0654 12.7502 27 12.75C26.8348 12.7501 26.6741 12.6956 26.543 12.595C26.4119 12.4945 26.3177 12.3534 26.275 12.1938C25.8693 10.6377 25.0561 9.21805 23.919 8.08101C22.782 6.94396 21.3623 6.13069 19.8062 5.72501C19.711 5.69962 19.6217 5.6557 19.5435 5.59577C19.4652 5.53585 19.3995 5.46108 19.3502 5.37575C19.3008 5.29042 19.2688 5.19621 19.2559 5.09849C19.243 5.00077 19.2495 4.90147 19.275 4.80626ZM18.8062 9.72501C20.625 10.21 21.79 11.375 22.275 13.1938C22.3177 13.3534 22.4119 13.4945 22.543 13.595C22.6741 13.6956 22.8348 13.7501 23 13.75C23.0654 13.7502 23.1305 13.7418 23.1937 13.725C23.289 13.6996 23.3783 13.6557 23.4565 13.5958C23.5348 13.5358 23.6005 13.4611 23.6498 13.3758C23.6992 13.2904 23.7312 13.1962 23.7441 13.0985C23.757 13.0008 23.7505 12.9015 23.725 12.8063C23.1 10.4675 21.5325 8.90001 19.1937 8.27501C19.0985 8.24957 18.9993 8.24312 18.9016 8.25605C18.8039 8.26898 18.7097 8.30102 18.6243 8.35036C18.452 8.44998 18.3264 8.61398 18.275 8.80626C18.2236 8.99854 18.2507 9.20336 18.3503 9.37566C18.45 9.54796 18.614 9.67362 18.8062 9.72501ZM28.7362 21.85C28.5221 23.4841 27.7202 24.9842 26.4805 26.07C25.2407 27.1558 23.648 27.753 22 27.75C12.2125 27.75 4.25 19.7875 4.25 10C4.24684 8.35256 4.84352 6.7603 5.92859 5.52066C7.01365 4.28101 8.51289 3.47876 10.1462 3.26376C10.5222 3.21807 10.9029 3.2956 11.2311 3.4847C11.5593 3.67381 11.8172 3.96429 11.9662 4.31251L14.6037 10.2C14.7202 10.4665 14.7684 10.7579 14.7439 11.0477C14.7195 11.3376 14.6232 11.6167 14.4637 11.86C14.4477 11.8848 14.4301 11.9086 14.4112 11.9313L11.7775 15.0638C11.7615 15.0962 11.7532 15.1319 11.7532 15.1681C11.7532 15.2043 11.7615 15.24 11.7775 15.2725C12.735 17.2325 14.79 19.2725 16.7775 20.2288C16.8107 20.2439 16.8469 20.2511 16.8834 20.2498C16.9198 20.2485 16.9555 20.2387 16.9875 20.2213L20.0737 17.5963C20.0958 17.5771 20.1192 17.5595 20.1437 17.5438C20.3859 17.3823 20.6646 17.2838 20.9544 17.2572C21.2443 17.2305 21.5362 17.2766 21.8037 17.3913L27.7087 20.0375C28.0523 20.1898 28.3378 20.4486 28.5228 20.7757C28.7079 21.1028 28.7827 21.4808 28.7362 21.8538V21.85ZM27.25 21.665C27.2542 21.6127 27.2418 21.5605 27.2147 21.5156C27.1876 21.4707 27.147 21.4356 27.0987 21.415L21.1925 18.7688C21.1602 18.7563 21.1257 18.751 21.0911 18.7531C21.0566 18.7553 21.023 18.7649 20.9925 18.7813L17.9075 21.4063C17.885 21.425 17.8612 21.4425 17.8375 21.4588C17.5859 21.6266 17.295 21.7263 16.9934 21.7482C16.6917 21.7701 16.3895 21.7134 16.1162 21.5838C13.8212 20.475 11.5337 18.2088 10.425 15.935C10.2946 15.6634 10.2365 15.3626 10.2565 15.0619C10.2764 14.7613 10.3736 14.4708 10.5387 14.2188C10.5548 14.1937 10.5728 14.1699 10.5925 14.1475L13.225 11.015C13.24 10.9823 13.2478 10.9467 13.2478 10.9106C13.2478 10.8746 13.24 10.839 13.225 10.8063L10.5925 4.91376C10.5751 4.86647 10.5439 4.82553 10.5028 4.79631C10.4618 4.76709 10.4129 4.75095 10.3625 4.75001H10.3337C9.06243 4.91912 7.89612 5.54519 7.05262 6.51129C6.20911 7.4774 5.74609 8.71749 5.75 10C5.75 18.96 13.04 26.25 22 26.25C23.2827 26.2539 24.5229 25.7907 25.4891 24.9469C26.4552 24.1032 27.0812 22.9366 27.25 21.665Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default NotepadIcon;
