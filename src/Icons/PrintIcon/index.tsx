import React from 'react';

const PrintIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 28 24" fill="none">
      <path
        d="M24.8337 5H23V1C23 0.734784 22.8946 0.48043 22.7071 0.292893C22.5196 0.105357 22.2652 0 22 0H6C5.73478 0 5.48043 0.105357 5.29289 0.292893C5.10536 0.48043 5 0.734784 5 1V5H3.16625C1.42 5 0 6.34625 0 8V18C0 18.2652 0.105357 18.5196 0.292893 18.7071C0.48043 18.8946 0.734784 19 1 19H5V23C5 23.2652 5.10536 23.5196 5.29289 23.7071C5.48043 23.8946 5.73478 24 6 24H22C22.2652 24 22.5196 23.8946 22.7071 23.7071C22.8946 23.5196 23 23.2652 23 23V19H27C27.2652 19 27.5196 18.8946 27.7071 18.7071C27.8946 18.5196 28 18.2652 28 18V8C28 6.34625 26.58 5 24.8337 5ZM7 2H21V5H7V2ZM21 22H7V16H21V22ZM26 17H23V15C23 14.7348 22.8946 14.4804 22.7071 14.2929C22.5196 14.1054 22.2652 14 22 14H6C5.73478 14 5.48043 14.1054 5.29289 14.2929C5.10536 14.4804 5 14.7348 5 15V17H2V8C2 7.44875 2.52375 7 3.16625 7H24.8337C25.4762 7 26 7.44875 26 8V17ZM23 10.5C23 10.7967 22.912 11.0867 22.7472 11.3334C22.5824 11.58 22.3481 11.7723 22.074 11.8858C21.7999 11.9994 21.4983 12.0291 21.2074 11.9712C20.9164 11.9133 20.6491 11.7704 20.4393 11.5607C20.2296 11.3509 20.0867 11.0836 20.0288 10.7926C19.9709 10.5017 20.0007 10.2001 20.1142 9.92597C20.2277 9.65189 20.42 9.41762 20.6666 9.2528C20.9133 9.08797 21.2033 9 21.5 9C21.8978 9 22.2794 9.15804 22.5607 9.43934C22.842 9.72064 23 10.1022 23 10.5Z"
        fill={props?.fill ?? '#00D9B1'}
      />
    </svg>
  );
};

export default PrintIcon;
