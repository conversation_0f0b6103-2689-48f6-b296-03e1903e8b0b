import React from 'react';

const PrivacyPolicy = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="18"
      height="21"
      viewBox="0 0 18 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.5 7.5H13.5V5.25C13.5 4.05653 13.0259 2.91193 12.182 2.06802C11.3381 1.22411 10.1935 0.75 9 0.75C7.80653 0.75 6.66193 1.22411 5.81802 2.06802C4.97411 2.91193 4.5 4.05653 4.5 5.25V7.5H1.5C1.10218 7.5 0.720644 7.65804 0.43934 7.93934C0.158035 8.22064 0 8.60218 0 9V19.5C0 19.8978 0.158035 20.2794 0.43934 20.5607C0.720644 20.842 1.10218 21 1.5 21H16.5C16.8978 21 17.2794 20.842 17.5607 20.5607C17.842 20.2794 18 19.8978 18 19.5V9C18 8.60218 17.842 8.22064 17.5607 7.93934C17.2794 7.65804 16.8978 7.5 16.5 7.5ZM6 5.25C6 4.45435 6.31607 3.69129 6.87868 3.12868C7.44129 2.56607 8.20435 2.25 9 2.25C9.79565 2.25 10.5587 2.56607 11.1213 3.12868C11.6839 3.69129 12 4.45435 12 5.25V7.5H6V5.25ZM16.5 19.5H1.5V9H16.5V19.5ZM10.125 14.25C10.125 14.4725 10.059 14.69 9.9354 14.875C9.81179 15.06 9.63608 15.2042 9.43052 15.2894C9.22495 15.3745 8.99875 15.3968 8.78052 15.3534C8.56229 15.31 8.36184 15.2028 8.2045 15.0455C8.04717 14.8882 7.94002 14.6877 7.89662 14.4695C7.85321 14.2512 7.87549 14.025 7.96064 13.8195C8.04578 13.6139 8.18998 13.4382 8.37498 13.3146C8.55999 13.191 8.7775 13.125 9 13.125C9.29837 13.125 9.58452 13.2435 9.79549 13.4545C10.0065 13.6655 10.125 13.9516 10.125 14.25Z"
        fill={props.fill ? props.fill : '#666666'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_3438_2191"
          x1="9"
          y1="0.75"
          x2="9"
          y2="21"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#00D9B1" />
          <stop offset="1" stop-color="#4DE4C8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default PrivacyPolicy;
