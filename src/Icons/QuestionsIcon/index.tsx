import React from 'react';

const QuestionsIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 26 27" fill="none">
      <path
        d="M14.5 20C14.5 20.2967 14.412 20.5867 14.2472 20.8334C14.0824 21.08 13.8481 21.2723 13.574 21.3858C13.2999 21.4993 12.9983 21.5291 12.7074 21.4712C12.4164 21.4133 12.1491 21.2704 11.9393 21.0607C11.7296 20.8509 11.5867 20.5836 11.5288 20.2926C11.471 20.0017 11.5007 19.7001 11.6142 19.426C11.7277 19.1519 11.92 18.9176 12.1667 18.7528C12.4133 18.588 12.7033 18.5 13 18.5C13.3978 18.5 13.7794 18.658 14.0607 18.9393C14.342 19.2206 14.5 19.6022 14.5 20ZM13 6.5C10.2425 6.5 8.00001 8.51875 8.00001 11V11.5C8.00001 11.7652 8.10536 12.0196 8.2929 12.2071C8.48044 12.3946 8.73479 12.5 9.00001 12.5C9.26522 12.5 9.51958 12.3946 9.70711 12.2071C9.89465 12.0196 10 11.7652 10 11.5V11C10 9.625 11.3463 8.5 13 8.5C14.6538 8.5 16 9.625 16 11C16 12.375 14.6538 13.5 13 13.5C12.7348 13.5 12.4804 13.6054 12.2929 13.7929C12.1054 13.9804 12 14.2348 12 14.5V15.5C12 15.7652 12.1054 16.0196 12.2929 16.2071C12.4804 16.3946 12.7348 16.5 13 16.5C13.2652 16.5 13.5196 16.3946 13.7071 16.2071C13.8946 16.0196 14 15.7652 14 15.5V15.41C16.28 14.9913 18 13.1725 18 11C18 8.51875 15.7575 6.5 13 6.5ZM26 13.5C26 16.0712 25.2376 18.5846 23.8091 20.7224C22.3807 22.8603 20.3503 24.5265 17.9749 25.5104C15.5995 26.4944 12.9856 26.7518 10.4638 26.2502C7.94208 25.7486 5.6257 24.5105 3.80762 22.6924C1.98953 20.8743 0.751405 18.5579 0.249797 16.0362C-0.251811 13.5144 0.0056327 10.9006 0.989572 8.52512C1.97351 6.14968 3.63975 4.11935 5.77759 2.6909C7.91543 1.26244 10.4288 0.5 13 0.5C16.4467 0.50364 19.7512 1.87445 22.1884 4.31163C24.6256 6.74882 25.9964 10.0533 26 13.5ZM24 13.5C24 11.3244 23.3549 9.19767 22.1462 7.38873C20.9375 5.57979 19.2195 4.16989 17.2095 3.33733C15.1995 2.50476 12.9878 2.28692 10.854 2.71136C8.72022 3.1358 6.76021 4.18345 5.22183 5.72183C3.68345 7.2602 2.63581 9.22022 2.21137 11.354C1.78693 13.4878 2.00477 15.6995 2.83733 17.7095C3.66989 19.7195 5.07979 21.4375 6.88873 22.6462C8.69767 23.8549 10.8244 24.5 13 24.5C15.9164 24.4967 18.7123 23.3367 20.7745 21.2745C22.8367 19.2123 23.9967 16.4164 24 13.5Z"
        fill="url(#paint0_linear_1462_520)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_520"
          x1="13"
          y1="0.5"
          x2="13"
          y2="26.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props.fill ?? '#00D9B1'} />
          <stop offset="1" stopColor={props.fill ?? '#4DE4C8'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default QuestionsIcon;
