import React from 'react';

const RoboIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width={props?.width ?? '59'}
      height={props?.height ?? '63'}
      viewBox="0 0 59 63"
      fill="none"
    >
      <g clip-path="url(#clip0_2671_1553)">
        <path
          d="M52.3755 34.8018C51.3727 35.6154 50.2738 36.2826 49.1489 36.6148C48.2293 36.8869 47.2966 37.0282 46.3276 37.1329C46.4653 41.5779 46.0704 51.3785 45.9769 53.6154C45.9613 53.9791 45.7587 54.3035 45.4444 54.484C44.0181 55.2898 40.3603 56.1768 39.3368 56.3547C32.2628 57.5922 22.3832 58.0657 15.7248 55.3265C15.2338 55.125 14.2129 54.6018 13.579 54.2721C13.2413 54.0968 13.0283 53.7541 13.0205 53.3721C12.9763 51.6218 12.8542 46.6273 12.8464 44.9058C12.836 42.266 12.9062 39.6366 12.9711 37.0073C11.7008 36.8712 10.5421 36.7038 9.54453 36.5076C8.42226 36.2852 7.38571 35.6259 6.46867 34.8227C6.34397 41.4445 6.93368 48.3227 6.60375 54.8869C6.6661 57.1657 8.39368 58.589 10.2511 59.6695C13.9843 61.8384 41.2306 66.859 51.3597 57.6811C51.8559 56.8962 52.3832 56.0643 52.404 55.0936C52.2585 48.3436 52.5521 41.5518 52.3755 34.8044V34.8018Z"
          fill="#00D9B1"
        />
        <path
          d="M58.7506 37.2346C58.3272 35.7878 56.4983 34.7936 54.7811 34.5896V32.4023C54.3733 32.889 53.9238 33.3834 53.4354 33.8544V50.1381C53.4354 50.9334 57.0776 49.9288 57.5348 49.6096C58.044 49.2538 58.787 47.5794 58.8572 46.9462C59.0234 45.4419 59.1014 38.4381 58.748 37.232L58.7506 37.2346Z"
          fill="#00D9B1"
        />
        <path
          d="M7.71823 26.3694C7.7546 26.3458 7.78058 26.3171 7.79876 26.2831C8.00399 25.922 8.1131 25.5165 8.34431 25.1685C8.37549 25.1214 8.36769 25.0534 8.41965 25.0037C8.43004 24.9932 8.68203 24.8467 8.69762 24.8441C8.81193 24.831 8.98598 24.8389 9.09509 24.8676C9.34449 24.9357 10.1109 25.4511 10.381 25.6107C10.5395 25.7049 10.9656 25.8775 11.0643 26.0057C11.3604 26.3929 11.137 27.1674 11.2072 27.6278C11.2175 27.6618 11.3422 27.7063 11.3864 27.722C12.1372 27.9601 13.1841 28.1197 13.9843 28.2531C17.1095 28.779 20.2009 28.9438 23.4249 29.0197C28.1192 29.1296 32.8525 29.14 37.5339 28.9412C39.1108 28.8732 40.8435 28.8836 42.3269 28.5932C42.5244 28.554 42.9608 28.5828 43.2076 28.5514C44.6234 28.3787 46.1796 28.0831 47.5304 27.7403C47.5798 27.7272 47.7227 27.6828 47.7513 27.6592C47.8422 27.5755 47.7435 26.563 47.7798 26.3458C47.824 26.0764 47.9981 25.9325 48.2397 25.7833C48.6657 25.5243 49.2164 25.1947 49.6867 24.9671C49.7412 24.9409 49.9101 24.8493 49.9231 24.8467C49.9932 24.8336 50.2556 24.8336 50.3257 24.8467C50.3621 24.8546 50.3621 24.9069 50.3933 24.9226C50.4478 24.9514 50.5232 24.9566 50.5699 25.0011C50.6193 25.0508 50.6141 25.1188 50.6453 25.1659C50.8401 25.4563 50.9908 25.8304 51.1519 26.1261C51.2116 26.2333 51.1259 26.3746 51.3571 26.3275C51.5285 26.1915 51.752 26.1025 51.9468 25.99C52.9626 25.3988 53.6744 24.7551 54.4096 23.9232C54.5602 23.7531 54.646 23.8447 54.6746 23.5386C54.7317 22.9447 54.7473 21.7386 54.6746 21.1578C54.6304 20.8098 54.1602 20.5665 53.8095 20.4331C53.7549 20.4121 53.5315 20.3258 53.5133 20.3206C53.3964 20.2892 53.2509 20.2813 53.1288 20.2761L53.0821 18.3845C52.5781 13.2618 49.6451 8.65457 44.613 5.64846C43.1063 4.74846 41.0774 3.75428 39.3783 3.29643C39.3342 3.28335 39.3524 3.18916 39.2251 3.22056L37.1858 21.681V2.37027C37.1858 2.37027 37.1078 2.38858 37.0922 2.3441C36.9857 2.03015 37.0013 1.813 36.7623 1.50166C35.8453 0.298174 33.7618 0.143813 32.1901 0.0705575C30.1092 -0.0262448 27.8646 -0.0393262 25.8123 0.143813C24.5861 0.253697 23.6795 0.397592 22.8196 0.973174C22.6663 1.07521 22.6611 1.16678 22.3702 1.1223L22.604 1.17986L22.4169 1.24003C22.4169 1.24003 22.5624 1.37085 22.2766 1.31591L22.3234 1.51213L22.2299 1.47288C22.261 1.61678 22.1052 1.7005 22.074 1.75544C22.0143 1.86794 21.9597 2.11387 21.887 2.20544C21.8688 2.22899 21.7986 2.19759 21.8038 2.23422C21.9363 2.26038 21.9571 2.39643 21.8038 2.35195L21.809 13.2828C21.6168 13.4397 21.7908 13.3168 21.809 13.4319L21.8038 21.6889L19.8476 3.31736C19.7048 3.11591 19.658 3.2912 19.6138 3.30428C11.8332 5.77666 6.5362 11.4069 5.91012 18.3897L5.86336 20.2839C5.74126 20.2865 5.59578 20.2944 5.47887 20.3284C5.46329 20.3336 5.26845 20.4095 5.21649 20.4304C4.86578 20.5613 4.4787 20.7496 4.33582 21.0636V23.6825C5.05802 24.9958 7.71563 26.372 7.71563 26.372L7.71823 26.3694Z"
          fill="#00D9B1"
        />
        <path
          d="M18.4812 43.4196C18.4812 45.7534 20.0841 47.6449 22.0637 47.6449C22.7833 47.6449 23.4535 47.3938 24.0172 46.9621C24.9992 46.2086 25.6487 44.9031 25.6487 43.4196C25.6487 42.1481 25.1707 41.0074 24.4173 40.233C23.7886 39.5868 22.9651 39.1943 22.0662 39.1943C20.0867 39.1943 18.4838 41.0859 18.4838 43.4196H18.4812Z"
          fill="#00D9B1"
        />
        <path
          d="M36.9181 47.6449C38.8967 47.6449 40.5006 45.7532 40.5006 43.4196C40.5006 41.0861 38.8967 39.1943 36.9181 39.1943C34.9396 39.1943 33.3357 41.0861 33.3357 43.4196C33.3357 45.7532 34.9396 47.6449 36.9181 47.6449Z"
          fill="#00D9B1"
        />
        <path
          d="M4.22409 34.6158C2.40558 34.7963 0.703986 35.6675 0.259752 37.4283C-0.10135 38.8646 -0.0390017 45.2954 0.155838 46.9411C0.316905 48.2858 1.06249 49.4213 2.44975 49.9498C2.90437 50.1225 5.56978 50.8263 5.56978 50.1408V34.0795L4.22409 32.7373V34.6158Z"
          fill="#00D9B1"
        />
        <path
          d="M56.7529 25.713C56.7373 25.7026 56.571 25.6005 56.558 25.5953C55.9995 25.4069 55.4383 25.2709 54.8486 25.0825C54.8071 25.0694 54.8278 24.9726 54.6486 25.0066L52.9678 26.3592C48.9073 28.8604 43.6155 29.4098 38.5522 29.6715H20.4426C17.5927 29.5223 14.839 29.3078 12.1268 28.7087C9.99397 28.2377 7.61693 27.4319 5.89975 26.2546C5.36199 25.8857 4.89697 25.4383 4.40857 25.0276C4.18776 24.9334 4.18516 25.0668 4.1384 25.0799C3.56687 25.263 2.97715 25.4069 2.429 25.5927C2.41861 25.5953 2.24975 25.6999 2.23417 25.7104C2.02114 25.8674 1.93281 26.0348 1.91982 26.2703C1.89904 26.6837 2.03413 27.1677 2.25495 27.6648C2.6732 28.6119 3.39021 29.6087 3.889 30.2627C3.99551 30.4014 4.10462 30.5453 4.21893 30.6944L5.56462 32.3139C5.86338 32.6409 6.17512 32.9627 6.50505 33.2636C7.4117 34.0904 8.43266 34.7654 9.53675 34.9956C10.5447 35.2049 11.7164 35.3802 12.9997 35.5267C22.4637 36.5967 37.9937 36.0473 37.9937 36.0473L38.0665 36.2043L38.095 36.0473C38.7964 36.0342 39.5031 36.0683 40.2045 36.0473C41.9944 36.0002 43.7895 35.9165 45.5509 35.736C45.7925 35.7124 46.0289 35.6863 46.2653 35.6575C47.2525 35.545 48.2059 35.3985 49.1437 35.1055C50.2504 34.7601 51.3311 34.0747 52.3183 33.2401C52.7028 32.913 53.0743 32.5651 53.4302 32.204C53.916 31.7069 54.368 31.1889 54.7759 30.6761C55.2227 30.1136 55.615 29.559 55.9371 29.0514C56.1917 28.6485 56.5035 28.1514 56.7295 27.6648C56.8542 27.3979 56.9555 27.1311 57.0048 26.8904C57.0932 26.4717 57.1503 26.0061 56.7425 25.7078L56.7529 25.713Z"
          fill="#00D9B1"
        />
      </g>
      <defs>
        <clipPath id="clip0_2671_1553">
          <rect
            width={props?.width ?? '59'}
            height={props?.height ?? '63'}
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default RoboIcon;
