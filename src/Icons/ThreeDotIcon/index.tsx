import React from 'react';

const ThreeDotIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 25 25" fill="none">
      <path
        d="M0.500001 2.125C0.500001 1.82833 0.587974 1.53832 0.752797 1.29165C0.917619 1.04497 1.15189 0.852713 1.42598 0.739181C1.70007 0.62565 2.00166 0.595945 2.29264 0.653823C2.58361 0.711701 2.85088 0.854562 3.06066 1.06434C3.27044 1.27412 3.4133 1.54139 3.47118 1.83237C3.52906 2.12334 3.49935 2.42494 3.38582 2.69903C3.27229 2.97311 3.08003 3.20738 2.83336 3.37221C2.58668 3.53703 2.29667 3.625 2 3.625C1.60218 3.625 1.22065 3.46697 0.939341 3.18566C0.658036 2.90436 0.500001 2.52283 0.500001 2.125ZM2 7C1.70333 7 1.41332 7.08797 1.16665 7.2528C0.919972 7.41762 0.727713 7.65189 0.614181 7.92598C0.50065 8.20006 0.470945 8.50166 0.528823 8.79264C0.586701 9.08361 0.729562 9.35088 0.939341 9.56066C1.14912 9.77044 1.41639 9.9133 1.70737 9.97118C1.99834 10.0291 2.29994 9.99935 2.57403 9.88582C2.84812 9.77229 3.08238 9.58003 3.24721 9.33336C3.41203 9.08668 3.5 8.79667 3.5 8.5C3.5 8.10218 3.34197 7.72065 3.06066 7.43934C2.77936 7.15804 2.39783 7 2 7ZM2 13.375C1.70333 13.375 1.41332 13.463 1.16665 13.6278C0.919972 13.7926 0.727713 14.0269 0.614181 14.301C0.50065 14.5751 0.470945 14.8767 0.528823 15.1676C0.586701 15.4586 0.729562 15.7259 0.939341 15.9357C1.14912 16.1454 1.41639 16.2883 1.70737 16.3462C1.99834 16.4041 2.29994 16.3744 2.57403 16.2608C2.84812 16.1473 3.08238 15.955 3.24721 15.7084C3.41203 15.4617 3.5 15.1717 3.5 14.875C3.5 14.4772 3.34197 14.0956 3.06066 13.8143C2.77936 13.533 2.39783 13.375 2 13.375Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default ThreeDotIcon;
