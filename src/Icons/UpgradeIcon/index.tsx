import React from 'react';

const UpgradeIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 30 23" fill="none">
      <path
        d="M29.9212 8.14377L25.2962 22.31C25.2497 22.4528 25.1717 22.5833 25.0678 22.6918C24.964 22.8003 24.8371 22.884 24.6964 22.9367C24.5558 22.9894 24.4051 23.0098 24.2556 22.9963C24.106 22.9828 23.9614 22.9358 23.8325 22.8588C23.8037 22.8413 20.6075 21 14.9975 21C9.3875 21 6.19125 22.8413 6.16 22.86C6.03113 22.9363 5.88677 22.9827 5.73758 22.9957C5.58839 23.0087 5.43818 22.9881 5.29804 22.9353C5.15789 22.8825 5.0314 22.7989 4.92789 22.6907C4.82438 22.5825 4.7465 22.4524 4.7 22.31L0.0774951 8.14002C-0.0156418 7.85636 -0.0228189 7.5515 0.0568667 7.26377C0.136552 6.97604 0.299542 6.71831 0.525334 6.52298C0.751126 6.32765 1.02964 6.20345 1.32584 6.166C1.62204 6.12855 1.92269 6.17953 2.19 6.31252L8.46375 9.43752L13.7037 0.73002C13.8384 0.506678 14.0286 0.321927 14.2557 0.193682C14.4828 0.0654365 14.7392 -0.00195312 15 -0.00195312C15.2608 -0.00195312 15.5172 0.0654365 15.7443 0.193682C15.9714 0.321927 16.1615 0.506678 16.2962 0.73002L21.5362 9.43752L27.8125 6.31252C28.0801 6.17933 28.3812 6.12843 28.6777 6.16623C28.9742 6.20404 29.2529 6.32886 29.4785 6.52493C29.7041 6.721 29.8666 6.97953 29.9454 7.26787C30.0242 7.55622 30.0158 7.86145 29.9212 8.14502V8.14377Z"
        fill={props?.fill ?? '#00d9b1'}
      />
    </svg>
  );
};

export default UpgradeIcon;
