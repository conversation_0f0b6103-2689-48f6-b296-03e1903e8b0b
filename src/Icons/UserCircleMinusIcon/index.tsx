import React from 'react';

const UserCircleMinusIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 32 28"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      width={24}
      height={24}
    >
      <path
        d="M21.2498 6.9999C21.2498 6.80098 21.3288 6.61022 21.4695 6.46957C21.6101 6.32891 21.8009 6.2499 21.9998 6.2499H27.9998C28.1987 6.2499 28.3895 6.32891 28.5301 6.46957C28.6708 6.61022 28.7498 6.80098 28.7498 6.9999C28.7498 7.19881 28.6708 7.38958 28.5301 7.53023C28.3895 7.67088 28.1987 7.7499 27.9998 7.7499H21.9998C21.8009 7.7499 21.6101 7.67088 21.4695 7.53023C21.3288 7.38958 21.2498 7.19881 21.2498 6.9999ZM28.5735 13.8749C29.0213 16.5378 28.6113 19.2741 27.4026 21.6887C26.194 24.1034 24.2492 26.0714 21.8491 27.3088C19.449 28.5461 16.7178 28.9887 14.0498 28.5727C11.3818 28.1566 8.91502 26.9034 7.00564 24.994C5.09627 23.0847 3.84308 20.6179 3.42702 17.9499C3.01097 15.2819 3.45356 12.5506 4.6909 10.1506C5.92824 7.75047 7.89632 5.80571 10.311 4.59704C12.7256 3.38837 15.4619 2.97834 18.1248 3.42615C18.2234 3.44091 18.318 3.47517 18.4032 3.52694C18.4884 3.5787 18.5624 3.64693 18.6209 3.72763C18.6794 3.80833 18.7213 3.89989 18.744 3.99694C18.7667 4.094 18.7698 4.19461 18.7532 4.2929C18.7366 4.39118 18.7006 4.48517 18.6472 4.56937C18.5938 4.65356 18.5242 4.72628 18.4424 4.78326C18.3607 4.84024 18.2683 4.88034 18.1709 4.90123C18.0734 4.92212 17.9727 4.92336 17.8748 4.9049C17.2551 4.80192 16.628 4.75007 15.9998 4.7499C13.8039 4.74874 11.6557 5.39062 9.82038 6.5963C7.98507 7.80199 6.54298 9.5187 5.67211 11.5345C4.80125 13.5504 4.53973 15.7771 4.91983 17.9399C5.29994 20.1027 6.30502 22.1068 7.81104 23.7049C8.98945 21.8726 10.7509 20.4907 12.811 19.7824C11.7837 19.0982 11.0039 18.1015 10.5868 16.9398C10.1697 15.7782 10.1375 14.5131 10.4949 13.3317C10.8523 12.1503 11.5804 11.1152 12.5716 10.3796C13.5627 9.64394 14.7642 9.24676 15.9985 9.24676C17.2328 9.24676 18.4344 9.64394 19.4255 10.3796C20.4166 11.1152 21.1447 12.1503 21.5022 13.3317C21.8596 14.5131 21.8274 15.7782 21.4103 16.9398C20.9932 18.1015 20.2133 19.0982 19.186 19.7824C21.2462 20.4907 23.0076 21.8726 24.186 23.7049C26.1549 21.6228 27.2512 18.8655 27.2498 15.9999C27.2496 15.3717 27.1977 14.7446 27.0948 14.1249C27.0763 14.0269 27.0776 13.9263 27.0985 13.8288C27.1193 13.7314 27.1594 13.639 27.2164 13.5572C27.2734 13.4754 27.3461 13.4058 27.4303 13.3525C27.5145 13.2991 27.6085 13.2631 27.7068 13.2465C27.8051 13.2298 27.9057 13.233 28.0027 13.2557C28.0998 13.2784 28.1914 13.3203 28.2721 13.3788C28.3528 13.4373 28.421 13.5113 28.4727 13.5965C28.5245 13.6817 28.5588 13.7763 28.5735 13.8749ZM15.9998 19.2499C16.8404 19.2499 17.6621 19.0006 18.361 18.5336C19.0599 18.0666 19.6046 17.4029 19.9263 16.6263C20.2479 15.8497 20.3321 14.9952 20.1681 14.1708C20.0041 13.3463 19.5994 12.5891 19.005 11.9947C18.4106 11.4003 17.6533 10.9955 16.8289 10.8316C16.0045 10.6676 15.15 10.7517 14.3734 11.0734C13.5968 11.3951 12.933 11.9398 12.466 12.6387C11.999 13.3376 11.7498 14.1593 11.7498 14.9999C11.7498 16.1271 12.1976 17.2081 12.9946 18.0051C13.7916 18.8021 14.8726 19.2499 15.9998 19.2499ZM15.9998 27.2499C18.5737 27.2539 21.0705 26.371 23.0698 24.7499C22.3363 23.5302 21.2998 22.5211 20.0609 21.8205C18.8221 21.1199 17.423 20.7517 15.9998 20.7517C14.5766 20.7517 13.1775 21.1199 11.9387 21.8205C10.6998 22.5211 9.66328 23.5302 8.92979 24.7499C10.9291 26.371 13.4258 27.2539 15.9998 27.2499Z"
        fill={props.fill ? props.fill : '#666666'}
      />
    </svg>
  );
};

export default UserCircleMinusIcon;
