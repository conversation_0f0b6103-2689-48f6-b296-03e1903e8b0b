import React from 'react';

const UserCirclePlusIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width={props?.width ?? '46'}
      height={props?.height ?? '46'}
      viewBox="0 0 46 46"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M31.75 7.25002C31.75 6.7859 31.9344 6.34078 32.2626 6.01259C32.5908 5.6844 33.0359 5.50002 33.5 5.50002H37V2.00002C37 1.5359 37.1844 1.09078 37.5126 0.762588C37.8408 0.434399 38.2859 0.250025 38.75 0.250025C39.2142 0.250025 39.6593 0.434399 39.9875 0.762588C40.3157 1.09078 40.5 1.5359 40.5 2.00002V5.50002H44C44.4642 5.50002 44.9093 5.6844 45.2375 6.01259C45.5657 6.34078 45.75 6.7859 45.75 7.25002C45.75 7.71415 45.5657 8.15927 45.2375 8.48746C44.9093 8.81565 44.4642 9.00002 44 9.00002H40.5V12.5C40.5 12.9642 40.3157 13.4093 39.9875 13.7375C39.6593 14.0656 39.2142 14.25 38.75 14.25C38.2859 14.25 37.8408 14.0656 37.5126 13.7375C37.1844 13.4093 37 12.9642 37 12.5V9.00002H33.5C33.0359 9.00002 32.5908 8.81565 32.2626 8.48746C31.9344 8.15927 31.75 7.71415 31.75 7.25002ZM45.435 19.2113C46.2345 23.9637 45.503 28.8473 43.346 33.1569C41.1891 37.4666 37.7183 40.9792 33.4348 43.1876C29.1514 45.3961 24.2769 46.1861 19.5152 45.4436C14.7535 44.7011 10.3509 42.4645 6.94322 39.0568C3.5355 35.6491 1.29893 31.2466 0.556444 26.4848C-0.186042 21.7231 0.603976 16.8487 2.81243 12.5652C5.02088 8.28176 8.5335 4.81099 12.8431 2.65403C17.1527 0.497069 22.0363 -0.234473 26.7888 0.565025C27.243 0.64536 27.6472 0.901818 27.9134 1.27861C28.1795 1.6554 28.2862 2.12205 28.21 2.57705C28.1339 3.03204 27.8812 3.43858 27.5069 3.70822C27.1326 3.97786 26.667 4.0888 26.2113 4.0169C23.4509 3.55255 20.6226 3.69528 17.923 4.43516C15.2234 5.17504 12.7175 6.4943 10.5797 8.30112C8.4418 10.1079 6.72333 12.3589 5.54386 14.8974C4.36439 17.4359 3.75224 20.2009 3.75003 23C3.74588 27.7123 5.47782 32.261 8.61503 35.7772C10.5668 32.9489 13.3112 30.7609 16.5032 29.4881C14.7886 28.1377 13.5374 26.2865 12.9237 24.192C12.31 22.0975 12.3642 19.8638 13.0788 17.8015C13.7934 15.7393 15.1329 13.951 16.911 12.6853C18.6891 11.4196 20.8175 10.7395 23 10.7395C25.1826 10.7395 27.3109 11.4196 29.089 12.6853C30.8671 13.951 32.2066 15.7393 32.9212 17.8015C33.6359 19.8638 33.6901 22.0975 33.0764 24.192C32.4627 26.2865 31.2115 28.1377 29.4969 29.4881C32.6889 30.7609 35.4333 32.9489 37.385 35.7772C40.5222 32.261 42.2542 27.7123 42.25 23C42.2501 21.9241 42.1608 20.85 41.9832 19.7888C41.9429 19.5611 41.9481 19.3278 41.9986 19.1022C42.049 18.8765 42.1437 18.6632 42.2771 18.4743C42.4104 18.2855 42.5799 18.125 42.7757 18.0021C42.9714 17.8791 43.1896 17.7962 43.4176 17.758C43.6457 17.7199 43.879 17.7273 44.1041 17.7798C44.3292 17.8323 44.5417 17.929 44.7293 18.0641C44.9169 18.1992 45.0758 18.3702 45.1969 18.5671C45.3181 18.764 45.399 18.9829 45.435 19.2113ZM23 28.25C24.3845 28.25 25.7379 27.8395 26.889 27.0703C28.0402 26.3011 28.9374 25.2079 29.4672 23.9288C29.997 22.6497 30.1356 21.2423 29.8655 19.8844C29.5954 18.5265 28.9287 17.2792 27.9498 16.3003C26.9708 15.3213 25.7235 14.6546 24.3657 14.3845C23.0078 14.1144 21.6003 14.2531 20.3212 14.7829C19.0422 15.3127 17.9489 16.2099 17.1797 17.361C16.4106 18.5122 16 19.8656 16 21.25C16 23.1065 16.7375 24.887 18.0503 26.1998C19.363 27.5125 21.1435 28.25 23 28.25ZM23 42.25C27.273 42.2543 31.4248 40.8299 34.795 38.2031C33.529 36.2232 31.785 34.5938 29.7237 33.4651C27.6624 32.3365 25.3501 31.7449 23 31.7449C20.6499 31.7449 18.3377 32.3365 16.2764 33.4651C14.215 34.5938 12.471 36.2232 11.205 38.2031C14.5753 40.8299 18.727 42.2543 23 42.25Z"
        fill="#45E3C4"
      />
      <defs>
        <linearGradient
          id="paint0_linear_3447_3620"
          x1="23.0175"
          y1="0.25"
          x2="23.0175"
          y2="45.715"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#00D9B1" />
          <stop offset="1" stop-color="#4DE4C8" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default UserCirclePlusIcon;
