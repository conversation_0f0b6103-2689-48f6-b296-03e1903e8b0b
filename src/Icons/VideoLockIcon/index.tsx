import React from 'react';

const VideoLockIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg width="62" height="62" {...props} viewBox="0 0 62 62" fill="none">
      <g filter="url(#filter0_i_4270_2370)" data-figma-bg-blur-radius="12.5">
        <circle cx="31" cy="31" r="30" fill="url(#paint0_linear_4270_2370)" />
        <circle
          cx="31"
          cy="31"
          r="30.3125"
          stroke="url(#paint1_linear_4270_2370)"
          stroke-opacity="0.2"
          stroke-width="0.625"
        />
      </g>
      <path
        d="M38.6552 24.6762H35.2644V22.1331C35.2644 20.7841 34.7286 19.4904 33.7747 18.5366C32.8209 17.5827 31.5272 17.0469 30.1782 17.0469C28.8293 17.0469 27.5356 17.5827 26.5817 18.5366C25.6279 19.4904 25.092 20.7841 25.092 22.1331V24.6762H21.7012C21.2516 24.6762 20.8203 24.8548 20.5024 25.1728C20.1844 25.4907 20.0058 25.9219 20.0058 26.3716V38.2394C20.0058 38.6891 20.1844 39.1203 20.5024 39.4383C20.8203 39.7562 21.2516 39.9348 21.7012 39.9348H38.6552C39.1049 39.9348 39.5361 39.7562 39.8541 39.4383C40.172 39.1203 40.3506 38.6891 40.3506 38.2394V26.3716C40.3506 25.9219 40.172 25.4907 39.8541 25.1728C39.5361 24.8548 39.1049 24.6762 38.6552 24.6762ZM30.1782 33.5771C29.9267 33.5771 29.6809 33.5025 29.4718 33.3628C29.2627 33.223 29.0997 33.0245 29.0035 32.7921C28.9072 32.5598 28.882 32.3041 28.9311 32.0574C28.9802 31.8108 29.1013 31.5842 29.2791 31.4064C29.4569 31.2286 29.6835 31.1075 29.9302 31.0584C30.1768 31.0093 30.4325 31.0345 30.6648 31.1308C30.8972 31.227 31.0958 31.39 31.2355 31.5991C31.3752 31.8082 31.4498 32.054 31.4498 32.3055C31.4498 32.6427 31.3158 32.9662 31.0773 33.2046C30.8389 33.4431 30.5155 33.5771 30.1782 33.5771ZM33.569 24.6762H26.7874V22.1331C26.7874 21.2338 27.1447 20.3713 27.7806 19.7354C28.4165 19.0995 29.2789 18.7423 30.1782 18.7423C31.0775 18.7423 31.94 19.0995 32.5759 19.7354C33.2118 20.3713 33.569 21.2338 33.569 22.1331V24.6762Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_i_4270_2370"
          x="-12.125"
          y="-12.125"
          width="86.25"
          height="86.25"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="0.912784"
            operator="dilate"
            in="SourceAlpha"
            result="effect1_innerShadow_4270_2370"
          />
          <feOffset dx="-0.456392" dy="0.456392" />
          <feGaussianBlur stdDeviation="0.228196" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_4270_2370"
          />
        </filter>
        <clipPath
          id="bgblur_0_4270_2370_clip_path"
          transform="translate(12.125 12.125)"
        >
          <circle cx="31" cy="31" r="30" />
        </clipPath>
        <linearGradient
          id="paint0_linear_4270_2370"
          x1="31"
          y1="1"
          x2="31"
          y2="61"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#00D9B1" />
          <stop offset="1" stop-color="#4DE4C8" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_4270_2370"
          x1="53.9839"
          y1="5.1129"
          x2="31"
          y2="61"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default VideoLockIcon;
