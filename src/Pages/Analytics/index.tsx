/* eslint-disable */
import { useState, useEffect } from 'react';
import { Breadcrumb } from '@Components/UI';
import { InputSelect } from '@Components/UI';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
  ComposedChart,
  Pie,
  PieChart,
  Cell,
} from 'recharts';
import CustomDatePicker from '@Components/UI/DatePicker';
import Skeleton from '@Components/UI/Skeleton';
import { DateConvertor2 } from '@Helpers/Utils';
import {
  useGetUserAnalytics,
  useGetCategoryWiseAnalytics,
  useGetCategoryWiseIssueAnalytics,
  useRectificationAnalytics,
} from '@Hooks/useAnalytics';

const breadcrumbItems = [
  { label: 'Home', link: '/' },
  { label: 'Analytic Data' },
];

const Analytics = (): JSX.Element => {
  const [userAnalyticsInputs, setUserAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [CWQAnalyticsInputs, setCWQAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [CWIAnalyticsInputs, setCWIAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const [RFAnalyticsInputs, setRFAnalyticsInputs] = useState<{
    timeRange: string;
    start_date?: string;
    end_date?: string;
  }>({ timeRange: 'week' });

  const userAntFilterKit =
    userAnalyticsInputs.timeRange +
    (userAnalyticsInputs.start_date && userAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(userAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(userAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const cwqAntFilterKit =
    CWQAnalyticsInputs.timeRange +
    (CWQAnalyticsInputs.start_date && CWQAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(CWQAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(CWQAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');

  const cwiAntFilterKit =
    CWIAnalyticsInputs.timeRange +
    (CWIAnalyticsInputs.start_date && CWIAnalyticsInputs.end_date
      ? `&start_date=${DateConvertor2(CWIAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(CWIAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
      : '');


      const rfAntFilterKit =
      RFAnalyticsInputs.timeRange +
      (RFAnalyticsInputs.start_date && RFAnalyticsInputs.end_date
        ? `&start_date=${DateConvertor2(RFAnalyticsInputs.start_date, 'YYYY-MM-DD')}&end_date=${DateConvertor2(RFAnalyticsInputs.end_date, 'YYYY-MM-DD')}`
        : '');

  const {
    data: userAnt,
    error: userAntError,
    isError: userAntIsError,
    refetch: userAntRefetch,
    isLoading: userAntIsLoading,
    isRefetching: userAntIsRefetching,
  } = useGetUserAnalytics(userAntFilterKit);
  const {
    data: CWQAnt,
    error: CWQError,
    isError: CWQIsError,
    refetch: CWQAntRefetch,
    isRefetching: CWQIsRefetching,
    isLoading: CWQIsLoading,
  } = useGetCategoryWiseAnalytics(cwqAntFilterKit);

  const {
    data: CWIAnt,
    error: CWIError,
    isError: CWIIsError,
    refetch: CWIAntRefetch,
    isRefetching: CWIIsRefetching,
    isLoading: CWIIsLoading,
  } = useGetCategoryWiseIssueAnalytics(cwiAntFilterKit);


  const {
    data: RFAnt,
    error: RFError,
    isError: RFIsError,
    refetch: RFAntRefetch,
    isRefetching: RFIsRefetching,
    isLoading: RFIsLoading,
  } = useRectificationAnalytics(rfAntFilterKit);
 

  const chartTitles = {
    users: 'Upload Overview',
    organisations: 'Organisation Growth',
    questions: 'Statewise Questions',
    documents: 'Statewise Uploaded Documents',
    categoryWiseQue: 'What People Are Asking to Rex (In %)',
    categoryWiseIssues: 'Categorized Issues from Reports (In %)',
    rectificationIssues: 'Rectification Issues (In %)',
  };

  const timeRangeOptions = [
    { label: 'Year', value: 'year' },
    { label: 'Month', value: 'month' },
    { label: 'Week', value: 'week' },
    { label: 'Custom', value: 'custom' },
  ];

  const colorPalette = [
    '#BEDDF1', // Pastel Sky
    '#DAD4B6', // Pastel Khaki
    '#E9C9AA', // Pastel Tan
    '#F1BEB5', // Pastel Flesh
    '#F8C57C', // Pastel Amber
    '#D7CAB7', // Pastel Earth
    '#A4D8D8', // Pastel Cyan
    '#D4C6AA', // Pastel Beige
    '#D3C7A2', // Pastel Sand
    '#F0EBD8', // Pastel Pearl
    '#D1FEB8', // Pastel Lime
    '#EFDFD8', // Pastel Skin
    '#F7DFC2', // Pastel Peach
    '#EBCCFF', // Pastel Mauve
    '#E7D7CA', // Pastel Nude
  ];

  useEffect(() => {
    if (!(userAnalyticsInputs.timeRange === 'custom')) {
      userAntRefetch();
    } else {
      if (userAnalyticsInputs.start_date && userAnalyticsInputs.end_date) {
        userAntRefetch();
      }
    }
  }, [userAnalyticsInputs]);

  useEffect(() => {
    if (!(CWQAnalyticsInputs.timeRange === 'custom')) {
      CWQAntRefetch();
    } else {
      if (CWQAnalyticsInputs.start_date && CWQAnalyticsInputs.end_date) {
        CWQAntRefetch();
      }
    }
  }, [CWQAnalyticsInputs]);

  useEffect(() => {
    if (!(CWIAnalyticsInputs.timeRange === 'custom')) {
      CWIAntRefetch();
    } else {
      if (CWIAnalyticsInputs.start_date && CWIAnalyticsInputs.end_date) {
        CWIAntRefetch();
      }
    }
  }, [CWIAnalyticsInputs]);

  useEffect(() => {
    if (!(RFAnalyticsInputs.timeRange === 'custom')) {
      RFAntRefetch();
    } else {
      if (RFAnalyticsInputs.start_date && RFAnalyticsInputs.end_date) {
        RFAntRefetch();
      }
    }
  }, [RFAnalyticsInputs]);

  return (
    <div className="flex flex-1 w-full h-full flex-col">
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Analytic Data</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </div>
      <div className="p-10 pt-0">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Users Chart Card */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
            <div className="px-6 pb-3 pt-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                {chartTitles.users}
              </h3>
              <div className="flex items-center gap-2">
                <InputSelect
                  containerClassName="pt-[0px]"
                  paddingBlock="p-3"
                  label=""
                  value={timeRangeOptions.find(
                    (result) => result.value === userAnalyticsInputs.timeRange
                  )}
                  onChange={(event) =>
                    setUserAnalyticsInputs({ timeRange: event.value })
                  }
                  options={timeRangeOptions}
                />

                {userAnalyticsInputs.timeRange === 'custom' && (
                  <CustomDatePicker
                    inputContainerClassName="py-[0.5px] w-[230px]"
                    containerClassName="pt-2"
                    value={{
                      startDate: userAnalyticsInputs.start_date as any,
                      endDate: userAnalyticsInputs.end_date as any,
                    }}
                    onChange={(range: any) => {
                      setUserAnalyticsInputs({
                        ...userAnalyticsInputs,
                        start_date: range.startDate,
                        end_date: range.endDate,
                      });
                    }}
                    isSingleMode={false}
                    placeholder="Choose a date range"
                  />
                )}
              </div>
            </div>
            <div className="p-6">
              <div className="h-64">
                {userAntIsLoading || userAntIsRefetching ? (
                  <Skeleton className="h-64" />
                ) : (userAnt?.data?.data?.trend?.length > 0 ?
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={userAnt?.data?.data?.trend}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <defs>
                        <linearGradient
                          id="questionGradient"
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="5%"
                            stopColor="#00d9b1"
                            stopOpacity={0.8}
                          />
                          <stop
                            offset="95%"
                            stopColor="#4de4c8"
                            stopOpacity={0.4}
                          />
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis dataKey="period" interval={1} />
                      <YAxis />
                      <Tooltip />
                      <Bar
                        dataKey="document_count"
                        name=" "
                        fill="url(#questionGradient)"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer> :
                  (
                  <div className="flex items-center justify-center h-full text-gray-500 text-sm font-medium w-full">
                    You haven't uploaded any reports during this time period.
                  </div>
                )
                )}
              </div>
            </div>
          </div>

       

          {/* Categorywise Issues Pie Chart Card */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 ">
            {/* Header Section */}
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                {chartTitles.categoryWiseIssues}
              </h3>
              {/* Time Range Selector */}
              <div className="flex items-center gap-2">
                <InputSelect
                  containerClassName="pt-[0px]"
                  paddingBlock="p-3"
                  label=""
                  value={timeRangeOptions.find(
                    (result) => result.value === CWIAnalyticsInputs.timeRange
                  )}
                  onChange={(event) =>
                    setCWIAnalyticsInputs({ timeRange: event.value })
                  }
                  options={[
                    { label: 'Year', value: 'year' },
                    { label: 'Month', value: 'month' },
                    { label: 'Week', value: 'week' },
                    { label: 'Custom', value: 'custom' },
                  ]}
                />

                {CWIAnalyticsInputs.timeRange === 'custom' && (
                  <CustomDatePicker
                    inputContainerClassName="py-[0.5px] w-[230px]"
                    containerClassName="pt-2"
                    value={{
                      startDate: CWIAnalyticsInputs.start_date as any,
                      endDate: CWIAnalyticsInputs.end_date as any,
                    }}
                    onChange={(range: any) => {
                      setCWIAnalyticsInputs({
                        ...CWIAnalyticsInputs,
                        start_date: range.startDate,
                        end_date: range.endDate,
                      });
                    }}
                    isSingleMode={false}
                    placeholder="Choose a date range"
                  />
                )}
              </div>
            </div>

            {/* Chart Section */}
            <div className="p-6">
              <div className="h-72 flex flex-col lg:flex-row">
                {CWIIsLoading || CWIIsRefetching ? (
                  <Skeleton className="h-72 w-full" />
                ) : CWIAnt?.data?.data?.domains?.filter(
                    (item: any) => item.questions_asked > 0
                  )?.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-500 text-sm font-medium w-full">
                    You haven't uploaded any reports during this time period.
                  </div>
                ) : (
                  <>
                    {/* Pie Chart Section */}
                    <div className="w-full lg:w-2/3 h-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Tooltip />
                          <Pie
                            data={CWIAnt?.data?.data?.domains.filter(
                              (item: any) => item.questions_asked > 0
                            )}
                            dataKey="questions_asked"
                            nameKey="domain_name"
                            cx="50%"
                            cy="50%"
                            outerRadius={100}
                            label={(entry) => `${entry.percentage}%`}
                          >
                            {CWIAnt?.data?.data?.domains.map((_: any, index: number) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={colorPalette[index % colorPalette.length]}
                              />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>

                    {/* Legend Section */}
                    <div className="w-full lg:w-1/3 flex flex-col justify-center pl-4 mt-4 text-sm font-medium max-h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                      {CWIAnt?.data?.data?.domains
                        .filter((item: any) => item.questions_asked > 0)
                        .map((item: any, index: number) => (
                          <div key={item.domain_id} className="flex items-center mb-1">
                            <div
                              className="mr-2 mt-2"
                              style={{
                                height:"12px",
                                width: "12px",
                                backgroundColor:
                                  colorPalette[index % colorPalette.length],
                              }}
                            />
                            <span style={{
                                color:
                                  colorPalette[index % colorPalette.length],
                              }}
                              className="text-xs mt-2">
                              {item.domain_name.length > 20 ? item.domain_name.slice(0, 17) + " ..." : item.domain_name} : {item.percentage}%
                            </span>
                          </div>
                        ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

             {/* Categorywise Questions Asked Bar Chart Card */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-800">
                {chartTitles.categoryWiseQue}
              </h3>
              <div className="flex items-center gap-2">
                <InputSelect
                  containerClassName="pt-[0px]"
                  paddingBlock="p-3"
                  label=""
                  value={timeRangeOptions.find(
                    (result) => result.value === CWQAnalyticsInputs.timeRange
                  )}
                  onChange={(event) =>
                    setCWQAnalyticsInputs({ timeRange: event.value })
                  }
                  options={[
                    { label: 'Year', value: 'year' },
                    { label: 'Month', value: 'month' },
                    { label: 'Week', value: 'week' },
                    { label: 'Custom', value: 'custom' },
                  ]}
                />

                {CWQAnalyticsInputs.timeRange === 'custom' && (
                  <CustomDatePicker
                    inputContainerClassName="py-[0.5px] w-[230px]"
                    containerClassName="pt-2"
                    value={{
                      startDate: CWQAnalyticsInputs.start_date as any,
                      endDate: CWQAnalyticsInputs.end_date as any,
                    }}
                    onChange={(range: any) => {
                      setCWQAnalyticsInputs({
                        ...CWQAnalyticsInputs,
                        start_date: range.startDate,
                        end_date: range.endDate,
                      });
                    }}
                    isSingleMode={false}
                    placeholder="Choose a date range"
                  />
                )}
              </div>
            </div>
            <div className="p-6">
              <div className="h-64 flex">
                {CWQIsLoading || CWQIsRefetching ? (
                  <Skeleton className="h-64 w-full" />
                ) : CWQAnt?.data?.data?.domains?.filter(
                    (item: any) => item.questions_asked > 0
                  )?.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-gray-500 text-sm font-medium w-full">
                    You haven't asked any questions during this time period.
                  </div>
                ) : (
                  <>
                    {/* Pie Chart Container */}
                    <div className="w-2/3 h-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Tooltip />
                          <Pie
                            data={CWQAnt?.data?.data?.domains.filter(
                              (item: any) => item.questions_asked > 0
                            )}
                            dataKey="questions_asked"
                            nameKey="domain_name"
                            cx="50%"
                            cy="50%"
                            outerRadius={100}
                            label={({ percentage }) => `${percentage}%`}
                          >
                            {CWQAnt?.data?.data?.domains.map((_: any, index: number) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={colorPalette[index % colorPalette.length]}
                              />
                            ))}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>

                    {/* Custom Legend */}
                    <div className="w-1/3 lg:w-1/3 flex flex-col justify-center text-sm font-medium pl-4 max-h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                      {CWQAnt?.data?.data?.domains
                        .filter((item: any) => item.questions_asked > 0)
                        .map((item: any, index: number) => (
                          <div key={item.domain_id} className="flex items-center mb-1">
                            <div
                              className="w-3 h-3 mr-2 mt-2"
                              style={{
                                backgroundColor:
                                  colorPalette[index % colorPalette.length],
                              }}
                            />
                            <span 
                              style={{
                                  color:
                                    colorPalette[index % colorPalette.length],
                                }}  
                              className="text-xs mt-2"
                            >
                              {item.domain_name.length > 20 ? item.domain_name.slice(0, 17) + " ..." : item.domain_name}: {item.percentage}%
                            </span>
                          </div>
                        ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>

             {/* -----  Chart Card */}
             {/* Rectification Issues Pie Chart Card */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-200 ">
          {/* Header Section */}
          <div className="px-6 py-4 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-800">
              {chartTitles.rectificationIssues}
            </h3>
            {/* Time Range Selector */}
            <div className="flex items-center gap-2">
              <InputSelect
                containerClassName="pt-[0px]"
                paddingBlock="p-3"
                label=""
                value={timeRangeOptions.find(
                  (result) => result.value === RFAnalyticsInputs.timeRange
                )}
                onChange={(event) =>
                  setRFAnalyticsInputs({ timeRange: event.value })
                }
                options={[
                  { label: 'Year', value: 'year' },
                  { label: 'Month', value: 'month' },
                  { label: 'Week', value: 'week' },
                  { label: 'Custom', value: 'custom' },
                ]}
              />
 
              {RFAnalyticsInputs.timeRange === 'custom' && (
                <CustomDatePicker
                  inputContainerClassName="py-[0.5px] w-[230px]"
                  containerClassName="pt-2"
                  value={{
                    startDate: RFAnalyticsInputs.start_date as any,
                    endDate: RFAnalyticsInputs.end_date as any,
                  }}
                  onChange={(range: any) => {
                    setRFAnalyticsInputs({
                      ...RFAnalyticsInputs,
                      start_date: range.startDate,
                      end_date: range.endDate,
                    });
                  }}
                  isSingleMode={false}
                  placeholder="Choose a date range"
                />
              )}
            </div>
          </div>
 
          {/* Chart Section */}
          <div className="p-6">
            <div className="h-72">
              {RFIsLoading || RFIsRefetching ? (
                <Skeleton className="h-72" />
              ) : RFAnt?.data?.data?.rectification_types?.filter(
                  (item: any) => item.issue_count > 0
                )?.length === 0 ? (
                <div className="flex items-center justify-center h-full text-gray-500 text-sm font-medium w-full">
                  Rectification Data Not Available for the Selected Time Range
                </div>
              ) : (
                <>
                  <div className="h-full flex flex-col lg:flex-row">
                    {/* Pie Chart Container */}
                    <div className="w-full lg:w-2/3 h-full">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Tooltip />
                          <Pie
                            data={RFAnt?.data?.data?.rectification_types.filter(
                              (item: any) => item.issue_count > 0
                            )}
                            dataKey="issue_count"
                            nameKey="rectification_type_name"
                            cx="50%"
                            cy="50%"
                            outerRadius={100}
                            label={(entry) => `${entry.percentage}%`}
                          >
                            {RFAnt?.data?.data?.rectification_types.map(
                              (_: any, index: number) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={colorPalette[index % colorPalette.length]}
                                />
                              )
                            )}
                          </Pie>
                        </PieChart>
                      </ResponsiveContainer>
                    </div>

                    {/* Custom Legend */}
                    <div className="w-full lg:w-1/3 flex flex-col justify-center pl-4 mt-4 text-sm font-medium max-h-full overflow-y-auto [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-gray-100 [&::-webkit-scrollbar-thumb]:bg-gray-300">
                      {RFAnt?.data?.data?.rectification_types
                        .filter((item: any) => item.issue_count > 0)
                        .map((item: any, index: number) => (
                          <div key={item.rectification_type_code} className="flex items-center mb-1">
                            <div
                              className="mr-2 mt-2"
                              style={{
                                height:"12px",
                                width: "12px",
                                backgroundColor:
                                  colorPalette[index % colorPalette.length],
                              }}
                            />
                            <span style={{
                                color:
                                  colorPalette[index % colorPalette.length],
                              }}
                              className="text-xs mt-2">
                              {item.rectification_type_name.length > 20 ? item.rectification_type_name.slice(0, 17) + " ..." : item.rectification_type_name} : {item.percentage}%
                            </span>
                          </div>
                        ))}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
