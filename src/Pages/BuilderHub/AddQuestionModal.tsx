import { But<PERSON>, InputField, InputSelect } from '@Components/UI';
import * as yup from 'yup';
import { Category, Post } from '.';
import { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { useNavigate } from 'react-router';
import { PATHS } from '@Config/Path.Config';

interface CategoryOption {
  label: string;
  value: string;
}

interface AddQuestionProps {
  onClose?: () => void;
  categories: Category[];
  post?: Post | null;
  setIsSuccess: (val: boolean) => void;
  isSinglePost?: boolean;
}

interface FormValues {
  description: string;
  topic: {
    label: string;
    value: string;
  }[];
}

const schema = yup.object().shape({
  description: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('Description is required')
    .max(2000, 'Description must have atmost 2000 characters'),
  topic: yup
    .array()
    .of(
      yup.object({
        label: yup.string().required('Label is required'),
        value: yup.string().required('Value is required'),
      })
    )
    .required('Topic is required') // optional but good to include
    .min(1, 'Topic is required'),
});

const AddQuestionModal: React.FC<AddQuestionProps> = ({
  onClose,
  categories = [],
  post = null,
  setIsSuccess,
  isSinglePost = false,
}) => {
  const api = new Api();
  const { addToast } = useToast();
  const navigate = useNavigate();
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
    defaultValues: {},
    mode: 'onChange',
  });

  useEffect(() => {
    if (post) {
      setValue('description', post?.content);
      if (post?.category_ids?.length) {
        const cat: CategoryOption[] = [];
        post?.category_ids.forEach((el) =>
          cat.push({ label: el?.name, value: String(el?.id) })
        );
        setValue('topic', cat);
      }
    }
  }, [post, setValue]);

  const getData = async (dataParams: {
    content: string;
    category_ids: string[];
  }) => {
    let response = null;
    if (!post) {
      const { data } = await api.post(API_PATHS.ADD_POST, {
        data: dataParams,
      });
      response = data;
    } else {
      const { data } = await api.patch(`${API_PATHS.ADD_POST}${post?.id}/`, {
        data: dataParams,
      });
      response = data;
    }
    return response;
  };

  const onSubmit = async (datas: FormValues) => {
    try {
      setIsLoading(true);
      const dataParams = {
        content: datas?.description,
        category_ids: datas?.topic?.map((item) => item.value),
      };

      // const { data } = await api.post(API_PATHS.ADD_POST, {
      //   data: dataParams,
      // });
      const data = await getData(dataParams);
      const { status, message } = data;
      if (status) {
        addToast('success', message);
        if (onClose) onClose();
        reset({
          description: '',
          topic: [],
        });
        if (isSinglePost && !post) {
          navigate(PATHS.BUILDER_HUB);
        } else {
          setIsSuccess(true);
        }
      } else {
        addToast('error', message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsLoading(false);
    }
    // navigate(PATHS.HOME);
  };

  useEffect(() => {
    if (categories?.length) {
      const cat: CategoryOption[] = [];
      categories.forEach(
        (el) =>
          el?.id !== 999 && cat.push({ label: el?.name, value: String(el?.id) })
      );
      setCategoryOptions([...cat]);
    }
  }, [categories]);

  return (
    <div className="flex flex-col px-1">
      <div className="text-sm text-secondary">
        Have a construction-related question? Ask the Rex Community!
      </div>
      <form
        className="flex flex-col gap-y-3 pt-4"
        onSubmit={handleSubmit(onSubmit)}
      >
        <Controller
          name="topic"
          control={control}
          render={({ field }) => (
            <InputSelect
              label="Topic"
              options={categoryOptions}
              placeholder="Select topic"
              isMulti
              field={field}
              errorMessage={errors.topic?.message}
            />
          )}
        />
        <Controller
          name="description"
          control={control}
          render={({ field }) => (
            <InputField
              label="Question"
              placeholder="Enter description here..."
              type="textarea"
              field={field}
              errorMessage={errors.description?.message}
            />
          )}
        />
        <Button
          text={!post ? 'Post' : 'Update'}
          className="mt-2"
          type="submit"
          loading={isLoading}
          disabled={isLoading}
        />
      </form>
    </div>
  );
};

export default AddQuestionModal;
