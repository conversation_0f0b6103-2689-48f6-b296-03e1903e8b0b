import { Button, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import parse from 'html-react-parser';
import { useEffect, useState } from 'react';

interface CodeOfConductModalProps {
  isOpen: boolean;
  onAccept: () => void;
}

const CodeOfConductModal = ({
  isOpen,
  onAccept,
}: CodeOfConductModalProps): JSX.Element => {
  const { addToast } = useToast();
  const api = new Api();
  const [contentData, setContentData] = useState<{
    title?: string;
    content?: string;
  } | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [isAccepting, setIsAccepting] = useState<boolean>(false);

  useEffect(() => {
    const fetchCodeOfConduct = async () => {
      try {
        const { data } = await api.get(API_PATHS.CODE_OF_CONDUCT);
        const codeOfConductContent = data?.data;

        if (codeOfConductContent) {
          setContentData(codeOfConductContent);
        }
      } catch (error: any) {
        addToast('error', error as string);
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      fetchCodeOfConduct();
    }
  }, [isOpen]);

  const handleAccept = async () => {
    try {
      setIsAccepting(true);
      const { data } = await api.patch(API_PATHS.ACCEPT_CODE_OF_CONDUCT, {
        data: {
          is_code_of_conduct_accepted: true,
        },
      });
      if (data?.status) {
        onAccept();
      } else {
        addToast('error', data?.message || 'Failed to accept code of conduct');
      }
    } catch (error: any) {
      addToast('error', error as string);
    } finally {
      setIsAccepting(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      size="xl"
      hideCloseButton
      outsideClickClose={false}
      header={contentData?.title || 'Code of Conduct'}
    >
      <div className="flex flex-col gap-6">
        <div
          className="reset-styles max-h-[500px] overflow-y-auto p-4 text-gray-700 leading-relaxed border border-gray-200 rounded-md"
          style={{ scrollBehavior: 'smooth' }}
        >
          {loading ? (
            <p className="text-center animate-pulse">Loading...</p>
          ) : contentData?.content ? (
            parse(contentData.content)
          ) : (
            <p className="text-center">No content available</p>
          )}
        </div>
        <div className="flex justify-end">
          <Button
            text="I Accept"
            onClick={handleAccept}
            loading={isAccepting}
            disabled={isAccepting}
          />
        </div>
      </div>
    </Modal>
  );
};

export default CodeOfConductModal;
