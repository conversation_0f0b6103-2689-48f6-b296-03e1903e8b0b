import React, { useEffect, useState, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import clsx from 'clsx';
import { X } from 'lucide-react';

import { Button, InputField, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import {
  useGetUserBusinessData,
  useUpdateBusinessData,
} from '@Query/Hooks/useEnterprise';
import ProfileLayout from '@Components/Layout/ProfileLayout';
import { UserCirclePlusIcon, Loader } from '@Icons';

import queryClient from '@Helpers/QueryClient';
import CacheKeys from '@Helpers/CacheKeys';
import VerifyOtp from '../VerifyOtp';
import { useSelector } from 'react-redux';

// Type Definitions
interface SelectOption {
  label: string;
  value: string;
  [key: string]: any;
}

interface RegisterFormValues {
  companyName: string;
  ABNno: string;
  countryCode: SelectOption;
  // address: string;
  street_name: string;
  street_no: string;
  suburb: string;
  city: string;
  post_code: string;
}

interface BusinessData {
  company_name: string;
  abn: string;
  short_live_logo_url: string;
  // address: string;
  street_name: string;
  street_no: string;
  suburb: string;
  city: string;
  post_code: string;
}

const BusinessDetails: React.FC = () => {
  const { addToast } = useToast();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [editToken, setEditToken] = useState<string>('');
  const [currentImage, setCurrentImage] = useState<string>('');
  const [originalBusinessData, setOriginalBusinessData] =
    useState<BusinessData | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isMobileChanged, setIsMobileChanged] = useState(false);
  const [isImageRemoved, setIsImageRemoved] = useState(false);
  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const [orgId, setOrgId] = useState<number>(0);
  const [isOrgAdmin, setIsOrgAdmin] = useState<boolean>(false);
  const [dataInitialized, setDataInitialized] = useState<boolean>(false);

  const userData = useSelector((state: any) => state.UserControle.user);

  useEffect(() => {
    if (userData) {
      if (userData?.org_id) {
        setOrgId(userData.org_id);
        setIsOrgAdmin(true);
      }
      setDataInitialized(true);
    }
  }, [userData]);

  const {
    data: viewProfileData,
    isError: isErrorInViewProfile,
    isLoading: isViewProfileLoading,
    refetch: refetchProfileData,
    error: errorOfViewProfile,
    isSuccess: isSuccessViewProfile,
  } = useGetUserBusinessData({
    orgId,
    enabled: dataInitialized,
    isOrgAdmin: isOrgAdmin,
  });

  const { mutate: updateUser, isLoading: isUpdateUserLoading } =
    useUpdateBusinessData({
      onSuccess: (data) => {
        setIsEdit(false);
        setIsImageRemoved(false);
        setSelectedFile(null);
        setIsImageUpdated(false);
        setPreviewUrl(null);
        if (isImageRemoved) {
          setCurrentImage('');
        }
        queryClient.invalidateQueries(CacheKeys.profileData);
        if (data?.data?.data?.is_mobile_updated) {
          setEditToken(data?.data?.data?.verify_token);
          setIsMobileChanged(true);
        }
        addToast('success', data?.data?.message);
        refetchProfileData();
      },
      onError: (error) => {
        addToast('error', error);
      },
    });

  const schema = yup.object().shape({
    companyName: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Company name is required')
      .min(3, 'Company name must have at least 3 characters')
      .max(50, 'Company name must have at most 50 characters'),
    ABNno: yup
      .string()
      .required('ABN number is required')
      .matches(/^\d{11}$/, 'ABN number must be exactly 11 digits'),
    // address: yup
    //   .string()
    //   .trim('Cannot include leading and trailing spaces')
    //   .strict(true)
    //   .required('Address is required')
    //   .max(255, 'Address must have at most 255 characters'),
    street_no: yup
      .string()
      .strict()
      .trim()
      .required('Street number is required')
      .matches(
        /^(?=.*[0-9])[0-9a-zA-Z/-]*[0-9][0-9a-zA-Z/-]*$/,
        'Street number must contain at least one number and can include letters, hyphens, and forward slashes'
      )
      .min(1, 'Street number must be at least 1 character')
      .max(12, 'Street number cannot exceed 12 characters')
      .test(
        'valid-street-no',
        'Please enter a valid street number',
        (value) => {
          if (!value) return true; // Skip validation if empty (required will catch this)

          // Australian street numbers can include:
          // - Simple numbers (1, 42)
          // - Unit numbers (1/42)
          // - Building numbers (G01, B2)
          // - Range numbers (42-44)
          // - Complex numbers (G01/42)
          return /^(?:(?:[G|B]\d{1,2}\/?)?\d+(?:[-\/]\d+)?|[G|B]\d{1,2})$/.test(
            value
          );
        }
      ),

    street_name: yup
      .string()
      .strict()
      .trim()
      .required('Street name is required')
      .matches(
        /^[a-zA-Z0-9\s'-]+$/,
        'Street name can only contain letters, numbers, spaces, hyphens and apostrophes'
      )
      .min(2, 'Street name must be at least 2 characters')
      .max(100, 'Street name cannot exceed 100 characters'),

    suburb: yup
      .string()
      .strict()
      .trim()
      .required('Suburb is required')
      .matches(
        /^[a-zA-Z\s'-]+$/,
        'Suburb can only contain letters, spaces, hyphens and apostrophes'
      )
      .min(2, 'Suburb must be at least 2 characters')
      .max(50, 'Suburb cannot exceed 50 characters'),

    city: yup
      .string()
      .strict()
      .trim()
      .required('City is required')
      .matches(
        /^[a-zA-Z\s'-]+$/,
        'City can only contain letters, spaces, hyphens and apostrophes'
      )
      .min(2, 'City must be at least 2 characters')
      .max(50, 'City cannot exceed 50 characters'),

    post_code: yup
      .string()
      .strict()
      .trim()
      .required('Post code is required')
      .matches(/^\d{4,6}$/, 'Post code must be between 4 and 6 digits'),
  });

  const {
    control,
    handleSubmit,
    clearErrors,
    formState: { errors },
    setValue,
    reset,
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      companyName: '',
      ABNno: '',
      // address: '',
      street_name: '',
      street_no: '',
      suburb: '',
      city: '',
      post_code: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (isSuccessViewProfile && viewProfileData?.data?.data) {
      const profileData = viewProfileData.data.data;

      setOriginalBusinessData(profileData);

      if (profileData?.short_live_logo_url) {
        setCurrentImage(profileData.short_live_logo_url);
      } else {
        setCurrentImage('');
      }

      setValue('companyName', profileData.company_name);
      setValue('ABNno', profileData.abn);
      // setValue('address', profileData.address);
      setValue('street_name', profileData.street_name);
      setValue('street_no', profileData.street_no);
      setValue('suburb', profileData.suburb);
      setValue('city', profileData.city);
      setValue('post_code', profileData.post_code);
    }

    if (isErrorInViewProfile) {
      addToast('error', errorOfViewProfile as string);
    }
  }, [
    viewProfileData,
    isSuccessViewProfile,
    isErrorInViewProfile,
    errorOfViewProfile,
    setValue,
    addToast,
  ]);

  const onSubmit = (data: RegisterFormValues) => {
    const formData = new FormData();

    formData.append('company_name', data.companyName);
    formData.append('abn', data.ABNno);
    formData.append('organization_id', orgId ? String(orgId) : '');
    formData.append('street_name', data.street_name);
    formData.append('street_no', data.street_no);
    formData.append('suburb', data.suburb);
    formData.append('city', data.city);
    formData.append('post_code', data.post_code);

    if (selectedFile) {
      formData.append('logo', selectedFile);
    }
    updateUser({
      formData,
      orgId: orgId.toString(),
      isOrgAdmin: isOrgAdmin,
    });
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);

      if (currentImage) {
        setIsImageUpdated(true);
      } else if (isImageRemoved) {
        setIsImageUpdated(true);
        setIsImageRemoved(false);
      }
      setCurrentImage('');
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const onEditSuccess = () => {
    setIsMobileChanged(false);
    setEditToken('');
    queryClient.invalidateQueries(CacheKeys.profileData);
  };

  const clearSelection = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setSelectedFile(null);
    setPreviewUrl(null);
    if (currentImage) {
      setCurrentImage('');
      setIsImageRemoved(true);
    } else if (isImageUpdated) {
      setIsImageUpdated(false);
      setIsImageRemoved(true);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleCancel = () => {
    setIsEdit(false);
    setSelectedFile(null);
    setIsImageRemoved(false);
    setIsImageUpdated(false);
    setPreviewUrl(null);
    clearErrors();

    if (originalBusinessData) {
      setCurrentImage(originalBusinessData.short_live_logo_url || '');
      reset({
        companyName: originalBusinessData.company_name,
        ABNno: originalBusinessData.abn,
        street_name: originalBusinessData.street_name,
        street_no: originalBusinessData.street_no,
        suburb: originalBusinessData.suburb,
        city: originalBusinessData.city,
        post_code: originalBusinessData.post_code,
      });
    }
  };

  return (
    <ProfileLayout>
      <div>
        <div className="flex flex-col justify-between sticky top-0 bg-white z-20">
          <div
            className={clsx(
              'flex justify-between gap-1.5 p-6 px-10 pb-0 h-[40px]'
            )}
          >
            <div className="flex justify-between w-full">
              <h3 className="text-xl font-semibold">Business Details</h3>
              {!isEdit &&
                userData.role !== 'org-user' &&
                userData.role !== 'sub-user' && (
                  <Button
                    text="Edit"
                    type="button"
                    className="!w-[65px] h-[40px] !p-2"
                    onClick={() => setIsEdit(true)}
                    disabled={isViewProfileLoading}
                  />
                )}
            </div>
          </div>
        </div>
        <div className="p-10 flex flex-col gap-4 ">
          <div className="max-w-[900px]">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="flex flex-col items-center">
                <div
                  className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center relative cursor-pointer overflow-hidden"
                  onClick={handleButtonClick}
                >
                  {previewUrl || currentImage ? (
                    <img
                      src={previewUrl || currentImage}
                      alt="Profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <UserCirclePlusIcon />
                  )}

                  <input
                    type="file"
                    ref={fileInputRef}
                    disabled={!isEdit}
                    className="hidden"
                    accept=".jpg,.jpeg,.png"
                    onChange={handleFileSelect}
                  />
                </div>
              </div>

              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="companyName"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Company Name"
                      field={field}
                      placeholder="First Name"
                      errorMessage={errors.companyName?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="ABNno"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="ABN No"
                      field={field}
                      placeholder="ABN No"
                      errorMessage={errors.ABNno?.message}
                      disabled={!isEdit}
                      type="number"
                    />
                  )}
                />
              </div>
              {/* <div className="flex gap-x-2 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="address"
                    control={control}
                    render={({ field }) => (
                      <TextAreaField
                        label={'Address'}
                        field={field}
                        placeholder="Address"
                        rows={6}
                        errorMessage={errors.address?.message}
                        disabled={!isEdit}
                      />
                    )}
                  />
                </div>
              </div> */}
              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="street_name"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Street Name"
                      field={field}
                      placeholder="Street Name"
                      errorMessage={errors.street_name?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="street_no"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Street No."
                      field={field}
                      placeholder="Street No."
                      errorMessage={errors.street_no?.message}
                      disabled={!isEdit}
                      type="number"
                    />
                  )}
                />
              </div>
              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="suburb"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Suburb"
                      field={field}
                      placeholder="Suburb"
                      errorMessage={errors.suburb?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="city"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="City"
                      field={field}
                      placeholder="City"
                      errorMessage={errors.city?.message}
                      disabled={!isEdit}
                    />
                  )}
                />
              </div>
              <div className="flex gap-x-4 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="post_code"
                    control={control}
                    render={({ field }) => (
                      <InputField
                        label="Postal Code"
                        field={field}
                        placeholder="Postal Code"
                        errorMessage={errors.post_code?.message}
                        disabled={!isEdit}
                      />
                    )}
                  />
                </div>
              </div>
              {isEdit && (
                <div className="mt-8 flex justify-center gap-10">
                  <div className="min-w-[150px]">
                    <Button
                      text="Cancel"
                      type="button"
                      className="w-full"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isUpdateUserLoading}
                    />
                  </div>
                  <div className="min-w-[150px]">
                    <Button
                      text="Save"
                      type="submit"
                      className="w-full"
                      loading={isUpdateUserLoading}
                    />
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
      {isMobileChanged && (
        <Modal
          isOpen={isMobileChanged}
          size="md"
          outsideClickClose={false}
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Verify OTP</h3>
            </div>
          }
          onClose={() => {
            setIsMobileChanged(false);
            queryClient.invalidateQueries(CacheKeys.profileData);
          }}
          children={
            <VerifyOtp
              isEdit={true}
              onEditSuccess={onEditSuccess}
              editToken={editToken}
            />
          }
        />
      )}
    </ProfileLayout>
  );
};

export default BusinessDetails;
