import { use<PERSON><PERSON>back, useEffect } from 'react';
import { <PERSON><PERSON>, InputField, InputSelect } from '@Components/UI';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import clsx from 'clsx';

import {
  useAddEnterpriseMember,
  useEditEnterpriseMember,
} from '@Query/Hooks/useEnterprise';
import { useToast } from '@Components/UI/Toast/ToastProvider';

import { COUNTRY_CODES } from '@Helpers/PhoneCountryCodes';

const DEFAULT_COUNTRY_CODE: SelectOption = {
  label: '+61',
  value: '+61',
  flag: 'https://flagcdn.com/w320/au.png',
};

interface LicenseFormat {
  format_id?: number;
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
}

interface StateData {
  id: number;
  name: string;
  license_formats: LicenseFormat[];
}

interface SelectOption {
  label: string;
  value: string;
  [key: string]: unknown;
}

interface UserData {
  id?: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile_no: string;
  license: string;
  state?: SelectOption & { license_formats?: LicenseFormat[] };
  license_format?: {
    prefix: string | null;
    postfix: string | null;
    min_number: number;
    max_number: number;
  };
  statesList?: StateData[];
  country_code?: string;
}

interface AddUsersProps {
  orgId?: number;
  isEdit?: boolean;
  userData?: UserData;
  handleSuccess?: () => void;
  handleClose?: () => void;
  type?: 'member' | 'sub-user';
}

const schema = {
  firstName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('First name is required')
    .min(3, 'First name must have at least 3 characters')
    .max(50, 'First name have at most 50 characters')
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed'),
  lastName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('Last name is required')
    .min(1, 'Last name must have atleast 1 characters')
    .max(50, 'Last name must have atmost 50 characters')
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed'),
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email'
    ),
  mobile_no: yup
    .string()
    .matches(/^[1-9][0-9]{7,11}$/, 'Enter a valid mobile number')
    .required('Mobile number is required'),
  countryCode: yup.mixed().required('Required'),
};

interface RegisterFormValues {
  orgId: number;
  firstName: string;
  lastName: string;
  email: string;
  mobile_no: string;
  countryCode: SelectOption;
}

interface RegisterRequestData {
  orgId?: number;
  userId?: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile_no: string;
  country_code: string;
  [key: string]: unknown;
}

const defaultUserData: UserData = {
  first_name: '',
  last_name: '',
  email: '',
  mobile_no: '',
  license: '',
  user_id: 0,
  id: 0,
};

const createSchema = () =>
  yup.object().shape({
    ...schema,
  });

const AddMembers = ({
  isEdit = false,
  orgId = 0,
  userData = defaultUserData,
  handleSuccess = () => null,
  handleClose = () => null,
  type = 'member',
}: AddUsersProps): JSX.Element => {
  const { addToast } = useToast();

  const {
    data: editEnterPriseMemberData,
    isLoading: editEnterpriseMemberIsLoading,
    isError: editEnterpriseMemberIsError,
    isSuccess: editEnterpriseMemberIsSuccess,
    error: editEnterpriseMemberError,
    mutate: editEnterpriseMember,
  } = useEditEnterpriseMember(type);

  const {
    data: addEnterPriseMemberData,
    isLoading: addEnterpriseMemberIsLoading,
    isError: addEnterpriseMemberIsError,
    isSuccess: addEnterpriseMemberIsSuccess,
    error: addEnterpriseMemberError,
    mutate: addEnterpriseMember,
  } = useAddEnterpriseMember(type);

  useEffect(() => {
    if (addEnterpriseMemberIsSuccess) {
      addToast('success', addEnterPriseMemberData?.data?.message);
      handleSuccess();
    }

    if (addEnterpriseMemberIsError) {
      addToast('error', addEnterpriseMemberError as string);
    }
  }, [
    addEnterPriseMemberData,
    addEnterpriseMemberIsError,
    addEnterpriseMemberError,
    addEnterpriseMemberIsSuccess,
  ]);

  useEffect(() => {
    if (editEnterpriseMemberIsSuccess) {
      addToast('success', editEnterPriseMemberData?.data?.message);
      handleSuccess();
    }

    if (editEnterpriseMemberIsError) {
      addToast('error', editEnterpriseMemberError as string);
    }
  }, [
    editEnterPriseMemberData,
    editEnterpriseMemberIsError,
    editEnterpriseMemberError,
    editEnterpriseMemberIsSuccess,
  ]);

  // Form setup
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormValues>({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    resolver: yupResolver(createSchema()) as any,
    defaultValues: {
      firstName: userData?.first_name || '',
      lastName: userData?.last_name || '',
      email: userData?.email || '',
      mobile_no: userData?.mobile_no || '',
      countryCode: userData?.country_code
        ? {
            label: userData?.country_code,
            value: userData?.country_code,
          }
        : DEFAULT_COUNTRY_CODE,
    },
    mode: 'onChange',
  });

  // Form submission handler
  const onSubmit = useCallback(
    async (formData: RegisterFormValues): Promise<void> => {
      const requestData: RegisterRequestData = {
        orgId: orgId,
        userId: type === 'sub-user' ? userData?.id : (userData.user_id ?? 0),
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        mobile_no: formData.mobile_no,
        country_code: String(formData.countryCode.value),
      };
      if (type === 'sub-user') {
        delete requestData['orgId'];
        if (!isEdit) {
          delete requestData['userId'];
        }
      }

      if (isEdit) {
        editEnterpriseMember(requestData);
        return;
      }
      addEnterpriseMember(requestData);
      return;
    },
    [addEnterpriseMember, editEnterpriseMember, isEdit, orgId, userData]
  );

  return (
    <form
      className={clsx('grid grid-cols-6 gap-4 pt-2')}
      onSubmit={handleSubmit(onSubmit)}
      autoComplete="off"
    >
      <div className={clsx('col-span-3')}>
        <Controller
          name="firstName"
          control={control}
          render={({ field }) => (
            <InputField
              label="First Name"
              field={field}
              placeholder="First Name"
              errorMessage={errors.firstName?.message}
              autoFocus
            />
          )}
        />
      </div>
      <div className={clsx('col-span-3')}>
        <Controller
          name="lastName"
          control={control}
          render={({ field }) => (
            <InputField
              label="Last Name"
              field={field}
              placeholder="Last Name"
              errorMessage={errors.lastName?.message}
            />
          )}
        />
      </div>
      <div className={clsx('col-span-3')}>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <InputField
              label="Email"
              field={field}
              placeholder="Enter your email"
              errorMessage={errors.email?.message}
            />
          )}
        />
      </div>
      <div className={clsx('col-span-3', 'relative mt-6')}>
        <div className="flex gap-x-2">
          <label
            className={clsx(
              'text-sm font-medium text-left absolute -top-5 text-gray-700'
            )}
          >
            Mobile Number
          </label>
          <div className="w-[110px]">
            <Controller
              name="countryCode"
              control={control}
              render={({ field }) => (
                <InputSelect
                  label={true}
                  field={field}
                  placeholder="+61"
                  containerClassName="pt-1 !gap-1"
                  errorMessage={
                    errors.countryCode?.message
                      ? String(errors.countryCode.message)
                      : undefined
                  }
                  smallDropdown={true}
                  options={COUNTRY_CODES}
                />
              )}
            />
          </div>

          <div className="w-full">
            <Controller
              name="mobile_no"
              control={control}
              render={({ field }) => (
                <InputField
                  label={true}
                  containerClassName="pt-0 !gap-1.5"
                  field={field}
                  placeholder="Enter your mobile number"
                  errorMessage={errors.mobile_no?.message}
                />
              )}
            />
          </div>
        </div>
      </div>
      <div
        className={clsx(
          'col-span-6 flex',
          isEdit ? 'justify-end' : 'justify-between'
        )}
      >
        {!isEdit && (
          <Button
            text="Cancel"
            type="reset"
            className="mt-2"
            variant="outline"
            onClick={() => handleClose()}
            disabled={
              editEnterpriseMemberIsLoading || addEnterpriseMemberIsLoading
            }
            width="w-[182px]"
          />
        )}

        <Button
          text={`${isEdit ? 'Save' : 'Create'}`}
          className="mt-2"
          width="w-[182px]"
          type="submit"
          loading={
            editEnterpriseMemberIsLoading || addEnterpriseMemberIsLoading
          }
        />
      </div>
    </form>
  );
};

export default AddMembers;
