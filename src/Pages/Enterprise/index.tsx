import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>put<PERSON><PERSON>,
  <PERSON><PERSON>,
  Breadcrumb,
} from '@Components/UI';
import {
  AddIcon,
  CloseIcon,
  DownloadIcon,
  ExportIcon,
  SearchIcon,
} from '@Icons';
import { useEffect, useRef, useState } from 'react';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import ContentLayout from '@Components/Layout/ContentLayout';
import ActionButtons from '@Components/Common/ActionButtons';
import { useDebounce } from '@Hooks/useDebouce';
import { useSelector } from 'react-redux';
import ViewMore from '@Components/UI/VIewMoreText';
import {
  useDeleteEnterpriseMember,
  useGetEnterpriseMember,
  useImportEnterpriseCsv,
  useSampleEnterpriseCsv,
  useToggleMember,
} from '@Query/Hooks/useEnterprise';

import AddMembers from './AddMembers';
import ReactSwitch from 'react-switch';
import clsx from 'clsx';

interface LicenseFormat {
  format_id?: number;
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
}

interface StateData {
  id: number;
  name: string;
  license_formats: LicenseFormat[];
}

interface SelectOption {
  label: string;
  value: string;
  [key: string]: unknown;
}

interface User {
  orgId: number;
  email: string;
  first_name: string;
  last_name: string;
  mobile_no: string;
  user_id: number;
  status: boolean;
  license: string;
  state?: SelectOption & { license_formats?: LicenseFormat[] };
  license_format?: {
    prefix: string | null;
    postfix: string | null;
    min_number: number;
    max_number: number;
  };
  statesList?: StateData[];
  country_code: any;
}

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface ImportErrorData {
  message?: string;
  data?: {
    invalid_sample_records: {
      errors: any;
      row: number;
      reason: string;
    }[];
  };
}

function EnterpriseMembers() {
  const importFileInputRef = useRef<HTMLInputElement>(null);
  const { addToast } = useToast();

  const [orgId, setOrgId] = useState<number>(0);
  const [dataInitialized, setDataInitialized] = useState<boolean>(false);
  const [openDeleteAccountModal, setOpenDeleteAccountModal] =
    useState<boolean>(false);
  const [openViewAccountModal, setOpenViewAccountModal] =
    useState<boolean>(false);
  const [searchText, setSearchText] = useState<string>('');
  const [openEditAccountModal, setOpenEditAccountModal] =
    useState<boolean>(false);
  const [openAddAccountModal, setOpenAddAccountModal] =
    useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<User | null>();
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'created_at', desc: true },
  ]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isImportError, setIsImportError] = useState(false);
  const [importErrorData, setImportErrorData] =
    useState<ImportErrorData | null>(null);
  const debouncedSearchText = useDebounce(searchText, 500);

  const userData = useSelector((state: any) => state.UserControle.user);

  useEffect(() => {
    if (userData?.org_id) {
      setOrgId(userData.org_id);
      setDataInitialized(true);
    }
  }, [userData]);

  const {
    data: subadmindatalist,
    isError: subadminlistError,
    isLoading: subadminlistIsLoading,
    refetch: refetchSubadminList,
    isFetching: subadminlistIsFetching,
    error: subadminListErrorMessage,
  } = useGetEnterpriseMember({
    orgId,
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : '-created_at',
    page: currentPage,
    limit: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
    enabled: dataInitialized && orgId > 0,
  });

  const {
    mutate: deleteUser,
    error: deleteUserError,
    isError: isErrorDeleteUser,
    isSuccess: isSuccessDeleteUser,
    data: deleteUserData,
    isLoading: isLoadingDeleteUser,
  } = useDeleteEnterpriseMember();

  const {
    mutate: sampleCsv,
    isLoading: sampleCsvLoading,
    isError: sampleCsvIsError,
    error: sampleCsvErrorMessage,
    isSuccess: sampleCsvSuccess,
  } = useSampleEnterpriseCsv();

  const {
    mutate: importItem,
    data: importItemData,
    isLoading: importItemIsLoading,
    isSuccess: importItemIsSuccess,
    isError: importItemIsError,
    error: importItemError,
  } = useImportEnterpriseCsv(
    {
      onError: () => { },
    },
    orgId
  );

  const {
    mutate: toggleMemberStatus,
    error: toggleError,
    data: toogleData,
    isError: isErrortoggle,
    isSuccess: isSuccesstoggle,
    isLoading: isLoadingtoggle,
  } = useToggleMember();

  useEffect(() => {
    if (dataInitialized && orgId > 0) {
      refetchSubadminList();
    }
  }, [dataInitialized, orgId, refetchSubadminList]);

  useEffect(() => {
    if (dataInitialized && orgId > 0) {
      refetchSubadminList();
    }
  }, [dataInitialized, sorting, pageSize, currentPage, debouncedSearchText]);

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  useEffect(() => {
    if (isErrortoggle) {
      addToast('error', toggleError as string);
    }
    if (isSuccesstoggle && dataInitialized) {
      addToast('success', toogleData?.data?.message);
      refetchSubadminList();
    }
  }, [isErrortoggle, toggleError, isSuccesstoggle, dataInitialized]);

  useEffect(() => {
    if (isSuccessDeleteUser && dataInitialized) {
      setSelectedRow(null);
      addToast('success', deleteUserData?.data?.message);
      if (currentData?.length === 1 && currentPage > 1) {
        setCurrentPage((prev) => prev - 1);
      } else {
        refetchSubadminList();
      }
      setOpenDeleteAccountModal(false);
    }
  }, [isSuccessDeleteUser, dataInitialized]);

  useEffect(() => {
    if (importItemIsError) {
      addToast('error', importItemError as string);
    }
    if (importItemIsSuccess && dataInitialized) {
      setIsImportError(false);
      addToast('success', importItemData?.data?.message);
      refetchSubadminList();
    }
  }, [
    importItemIsError,
    importItemIsSuccess,
    importItemData,
    importItemError,
    dataInitialized,
  ]);

  useEffect(() => {
    if (sampleCsvIsError) {
      addToast('error', sampleCsvErrorMessage as string);
    }
    if (sampleCsvSuccess) {
      addToast('success', 'Sample CSV generated successfully');
    }
  }, [sampleCsvIsError, sampleCsvErrorMessage, sampleCsvSuccess]);

  useEffect(() => {
    if (subadminlistError) {
      addToast('error', subadminListErrorMessage as string);
    }
    if (isErrorDeleteUser) {
      addToast('error', deleteUserError as string);
    }
  }, [
    subadminlistError,
    subadminListErrorMessage,
    isErrorDeleteUser,
    deleteUserError,
  ]);

  const handleSampleCsv = () => {
    sampleCsv(undefined, {
      onSuccess: (res: { data: string }) => {
        const blob = new Blob([res.data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sampleUsers.csv';
        a.click();
        window.URL.revokeObjectURL(url);
      },
    });
  };

  const importHandler = () => {
    if (importFileInputRef.current) {
      importFileInputRef.current.value = '';
      importFileInputRef.current.click();
    }
  };

  const handleImportErrorModalClose = () => {
    setIsImportError(false);
  };

  const handleImportCsv = (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    importItem(formData);
  };

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  const currentData = subadmindatalist?.data?.data?.list;

  const columns: SearchableColumnDef<User>[] = [
    {
      header: 'Name',
      accessorKey: 'first_name',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore
              text={`${row?.first_name} ${row?.last_name}`}
              maxLength={30}
            />
          </div>
        );
      },
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row?.email} maxLength={30} />
          </div>
        );
      },
    },
    {
      header: 'Phone number',
      accessorKey: 'mobile_no',
      enableSorting: false,
      cell: (info) => {
        return (
          <text>
            {info?.row?.original?.country_code} {info?.row?.original?.mobile_no}
          </text>
        );
      },
    },
    {
      header: 'Number of Uploads Used',
      accessorKey: 'uploaded_count',
      cell: (info) => info.getValue() ?? 0,
    },
    {
      header: 'Status',
      accessorKey: 'status',
      enableSorting: false,
      cell: (info) => {
        return (
          <div className="flex gap-4">
            <ReactSwitch
              checked={info.row?.original?.status}
              onChange={() =>
                toggleMemberStatus({
                  orgId: orgId,
                  memberId: info.row.original.user_id,
                })
              }
              disabled={isLoadingtoggle || !dataInitialized}
              onColor="#E0E3FF"
              onHandleColor="#00d9b1"
              offColor="#E8ECF3"
              offHandleColor="#D0D5DD"
              handleDiameter={24}
              uncheckedIcon={false}
              checkedIcon={false}
              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
              height={16}
              width={40}
            />
          </div>
        );
      },
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          deleteIcon
          editIcon
          viewIcon
          onView={() => {
            setSelectedRow({ ...info.row.original });
            setOpenViewAccountModal(true);
          }}
          onDelete={() => {
            setSelectedRow({ ...info.row.original });
            setOpenDeleteAccountModal(true);
          }}
          onEdit={() => {
            setSelectedRow({ ...info.row.original });
            setOpenEditAccountModal(true);
          }}
        />
      ),
    },
  ];

  const breadcrumbItems = [
    { label: 'Home', link: '/' },
    { label: 'Enterprise Management', link: '/enterprise' },
  ];

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">
            Enterprise Management
          </span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </div>
      <ContentLayout
        headerRightSideChildren={
          <div className="w-4xl flex items-center justify-end gap-5">
            <div className="w-[300px]">
              <InputField
                placeholder="Search by Name, Email, Phone"
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>
            <div className="flex gap-5">
              <Button
                text={
                  <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                    <DownloadIcon height={18} width={18} fill="#fff" />
                    Sample CSV
                  </div>
                }
                loading={sampleCsvLoading}
                onClick={() => handleSampleCsv()}
                disabled={sampleCsvLoading || !dataInitialized}
              />

              <>
                <Button
                  text={
                    <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                      <ExportIcon height={18} width={18} fill="#fff" />
                      Import
                    </div>
                  }
                  onClick={() => importHandler()}
                  loading={importItemIsLoading}
                  disabled={importItemIsLoading || !dataInitialized}
                />
                <input
                  type="file"
                  accept=".csv"
                  ref={importFileInputRef}
                  className="hidden"
                  onChange={(e) => {
                    if (e.target.files && e.target.files[0]) {
                      handleImportCsv(e.target.files[0]);
                      e.target.value = '';
                    }
                  }}
                />
              </>

              <Button
                text={
                  <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                    <AddIcon height={18} width={18} fill="#fff" /> Add Member
                  </div>
                }
                onClick={() => setOpenAddAccountModal(true)}
                disabled={!dataInitialized}
              />
            </div>
          </div>
        }
      >
        <DataTable
          data={currentData}
          columns={columns}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={subadmindatalist?.data?.data?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
          loading={
            !dataInitialized || subadminlistIsLoading || subadminlistIsFetching
          }
        />

        <Modal
          isOpen={openViewAccountModal}
          size="xl"
          className="max-w-xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">View Member</h3>
            </div>
          }
          onClose={() => {
            setOpenViewAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-x-10 gap-y-6 rounded-md">
              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">First Name</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.first_name}
                </p>
              </div>
              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">Last Name</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.last_name}
                </p>
              </div>

              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">Email</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.email}
                </p>
              </div>
              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">Phone Number</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.country_code} {selectedRow?.mobile_no}
                </p>
              </div>

              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">
                  Number Of Uploads used
                </p>
                <p className="text-gray-800 font-normal break-words">0</p>
              </div>
            </div>
          }
        />

        <Modal
          isOpen={openDeleteAccountModal}
          onClose={() => {
            setOpenDeleteAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure you want to delete this member? This action cannot
                be undone.
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  loading={isLoadingDeleteUser}
                  text="Delete"
                  variant="other"
                  className="h-[50px] w-[50px] border border-[#FF0000]
              text-[#FF0000] bg-transparent hover:border-[#FF0000]"
                  onClick={() =>
                    deleteUser({
                      orgId: orgId,
                      listId: Number(selectedRow?.user_id),
                    })
                  }
                />
                <Button
                  text="Cancel"
                  variant="outline"
                  disabled={isLoadingDeleteUser}
                  onClick={() => {
                    setOpenDeleteAccountModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />

        <Modal
          isOpen={openAddAccountModal}
          size="none"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Add Member</h3>
            </div>
          }
          onClose={() => {
            setOpenAddAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddMembers
              orgId={orgId}
              handleClose={() => {
                setOpenAddAccountModal(false);
              }}
              handleSuccess={() => {
                setOpenAddAccountModal(false);
                refetchSubadminList();
              }}
            />
          }
        />

        <Modal
          isOpen={openEditAccountModal}
          size="none"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Edit Member</h3>
            </div>
          }
          onClose={() => {
            setOpenEditAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddMembers
              isEdit
              orgId={orgId}
              handleSuccess={() => {
                setOpenEditAccountModal(false);
                refetchSubadminList();
              }}
              userData={
                selectedRow ?? {
                  email: '',
                  first_name: '',
                  last_name: '',
                  mobile_no: '',
                  license: '',
                  id: 0,
                  country_code: '',
                  statesList: [],
                  state: { label: '', value: '' },
                  user_id: 0,
                  license_format: {
                    prefix: null,
                    postfix: null,
                    min_number: 0,
                    max_number: 0,
                  },
                }
              }
            />
          }
        />

        <Modal
          isOpen={isImportError}
          size="xl"
          onClose={() => {
            handleImportErrorModalClose();
          }}
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">
                Members Import Status
              </h3>
              <p className="mt-2 text-sm text-gray-600">
                {importErrorData?.message}
              </p>
            </div>
          }
          children={
            <div className="overflow-auto">
              <div className="space-y-4">
                <div className="text-sm font-medium">
                  The following membes could not be imported:
                </div>
                <div className="border">
                  <table className="w-full">
                    <thead className="bg-gray-200">
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                          Row
                        </th>
                        <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">
                          Reason
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y">
                      {importErrorData?.data?.invalid_sample_records?.map(
                        (errorItem, index) => {
                          return (
                            <tr key={index} className="bg-white">
                              <td className="px-4 py-3 text-sm text-gray-900">
                                {errorItem.row}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900">
                                <div className="space-y-1">
                                  {Object.entries(errorItem.errors || {}).map(
                                    ([errorKey, errorMessages]) => (
                                      <div key={errorKey}>
                                        <span className="font-medium">
                                          {errorKey}:{' '}
                                        </span>
                                        {Array.isArray(errorMessages) ? (
                                          <ul className="list-disc pl-5">
                                            {errorMessages.map(
                                              (message, msgIndex) => (
                                                <li key={msgIndex}>
                                                  {message}
                                                </li>
                                              )
                                            )}
                                          </ul>
                                        ) : (
                                          <span>{String(errorMessages)}</span>
                                        )}
                                      </div>
                                    )
                                  )}
                                </div>
                              </td>
                            </tr>
                          );
                        }
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          }
        />
      </ContentLayout>
    </div>
  );
}

export default EnterpriseMembers;
