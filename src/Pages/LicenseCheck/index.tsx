import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button, Modal, InputSelect, InputField } from '@Components/UI';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate } from 'react-router';
import clsx from 'clsx';
import { PATHS } from '@Config/Path.Config';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { useSelector } from 'react-redux';

interface SelectOption {
  label: string;
  value: string;
  [key: string]: any;
}

interface LicenseFormat {
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
  format_id?: string;
}

interface LicenseFormValues {
  licenseNo: string;
  licensePrefix: SelectOption | null;
  licensePostfix: SelectOption | null;
  licenseFormat: LicenseFormat | null;
  state?: any;
  currentLicense?: string;
}

interface UserData {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile_no: string;
  country_code: string;
  role: string;
  verify_token: string;
  user_license: string;
  state: number;
  state_name: string;
  country: number;
  country_name: string;
  format_id: number;
  prefix: string | null;
  postfix: string | null;
  license_status: string;
  is_email_notification: boolean;
  is_push_notification: boolean;
}

const LicenseCheck = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}): JSX.Element => {
  const navigate = useNavigate();
  const api = useRef(new Api()).current;
  const { addToast } = useToast();

  const [loading, setLoading] = useState<boolean>(false);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [formatsLoaded, setFormatsLoaded] = useState<boolean>(false);
  const [stateFormats, setStateFormats] = useState<any>(null);
  const [prefixOptions, setPrefixOptions] = useState<SelectOption[]>([]);
  const [postfixOptions, setPostfixOptions] = useState<SelectOption[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<LicenseFormat | null>(
    null
  );
  const [previousStateId, setPreviousStateId] = useState<number | null>(null);
  const [currentLicense, setCurrentLicense] = useState<string>('');

  const storedUser = useSelector((state: any) => state.UserControle.user);

  useEffect(() => {
    if (storedUser?.state) {
      setUserData(storedUser);
    }
  }, [storedUser]);

  useEffect(() => {
    if (userData?.state && userData.state !== previousStateId) {
      fetchStateFormats(userData.state);
      setPreviousStateId(userData.state);

      if (userData.user_license) {
        setCurrentLicense(userData.user_license);
        setLicenseValue('currentLicense', userData.user_license);
        setLicenseValue('licenseNo', userData.user_license);
      }
    }
  }, [userData]);

  useEffect(() => {
    if (stateFormats?.length) {
      const prefixList = stateFormats
        .filter((item: any) => item.prefix)
        .map((item: any) => ({
          label: item.prefix,
          value: item.prefix,
          ...item,
        }));

      const postfixList = stateFormats
        .filter((item: any) => item.postfix)
        .map((item: any) => ({
          label: item.postfix,
          value: item.postfix,
          ...item,
        }));

      setPrefixOptions(prefixList);
      setPostfixOptions(postfixList);

      // Auto-select first option for prefix if available
      if (prefixList.length > 0) {
        const firstPrefixOption = prefixList[0];
        setLicenseValue('licensePrefix', firstPrefixOption);

        // Find matching format based on the first prefix
        const formatForPrefix = stateFormats.find(
          (item: any) => item.prefix === firstPrefixOption.value
        );
        if (formatForPrefix) {
          setSelectedFormat(formatForPrefix);
          setLicenseValue('licenseFormat', formatForPrefix);
        }
      }

      // Auto-select first option for postfix if available
      if (postfixList.length > 0) {
        const firstPostfixOption = postfixList[0];
        setLicenseValue('licensePostfix', firstPostfixOption);

        // If no format was selected from prefix, try to find one based on postfix
        if (!selectedFormat && !prefixList.length) {
          const formatForPostfix = stateFormats.find(
            (item: any) => item.postfix === firstPostfixOption.value
          );
          if (formatForPostfix) {
            setSelectedFormat(formatForPostfix);
            setLicenseValue('licenseFormat', formatForPostfix);
          }
        }
      }

      if (
        stateFormats.length === 1 &&
        !stateFormats[0].prefix &&
        !stateFormats[0].postfix
      ) {
        setSelectedFormat(stateFormats[0]);
        setLicenseValue('licenseFormat', stateFormats[0]);
      }

      setTimeout(() => {
        triggerLicense('licenseNo');
      }, 0);
    }
  }, [stateFormats]);

  const fetchStateFormats = async (stateId: number) => {
    try {
      setFormatsLoaded(false);
      const { data } = await api.get(
        `${API_PATHS.FOEMAT_STATE_LIST}${stateId}/`
      );
      setStateFormats(data.data);
      setFormatsLoaded(true);
    } catch (error) {
      addToast('error', `Failed to fetch state formats: ${error}`);
      setFormatsLoaded(true);
    }
  };

  const licenseSchema = yup.object().shape({
    licenseNo: yup
      .string()
      .required('License number is required')
      .matches(/^\d+$/, 'License number must contain only numbers')
      .test('len', 'License number must match format range', function (value) {
        const { licenseFormat } = this.parent;
        if (!value || !licenseFormat) return true;

        const minLength = licenseFormat.min_number;
        const maxLength = licenseFormat.max_number;

        const isValid = value.length >= minLength && value.length <= maxLength;

        if (!isValid) {
          if (minLength === maxLength) {
            return this.createError({
              message: `License number must be exactly ${minLength} characters`,
              path: this.path,
            });
          }
          return this.createError({
            message: `License number must be between ${minLength} and ${maxLength} characters`,
            path: this.path,
          });
        }

        return isValid;
      })
      .test(
        'not-same-as-current',
        'License Number cannot be same as the previous one',
        function (value) {
          const { currentLicense } = this.parent;
          return value !== currentLicense;
        }
      ),
    licensePrefix:
      prefixOptions.length > 0
        ? yup
            .object({
              label: yup.string().required(),
              value: yup.string().required(),
            })
            .nullable()
            .required('License prefix is required')
        : yup.mixed().nullable(),
    licensePostfix:
      postfixOptions.length > 0
        ? yup
            .object({
              label: yup.string().required(),
              value: yup.string().required(),
            })
            .nullable()
            .required('License postfix is required')
        : yup.mixed().nullable(),
    licenseFormat: yup.object().nullable(),
    currentLicense: yup.string(),
  });

  const {
    control: licenseControl,
    handleSubmit: handleLicenseSubmit,
    formState: { errors: licenseErrors },
    watch: watchLicense,
    setValue: setLicenseValue,
    trigger: triggerLicense,
    getValues: getLicenseValues,
    reset: resetLicense,
  } = useForm<LicenseFormValues>({
    resolver: yupResolver(licenseSchema) as any,
    defaultValues: {
      licenseNo: '',
      licensePrefix: null,
      licensePostfix: null,
      licenseFormat: null,
      currentLicense: '',
    },
    mode: 'onChange',
  });

  const onSubmitLicense = useCallback(async (formData: LicenseFormValues) => {
    setLoading(true);
    try {
      const submitData = {
        license: formData.licenseNo,
        format_id: formData?.licenseFormat?.format_id,
      };
      const { data } = await api.post(API_PATHS.USER_LICENSE_UPDATE, {
        data: submitData,
      });
      const currentPath = location.pathname;
      addToast('success', data.message);
      if (currentPath === PATHS.PROFILE) {
        window.dispatchEvent(new Event('refreshProfileData'));
      } else {
        navigate(PATHS.PROFILE);
      }
    } catch (error) {
      addToast('error', error as string);
    } finally {
      setLoading(false);
    }
  }, []);

  const handlePrefixChange = (selected: SelectOption) => {
    setLicenseValue('licensePrefix', selected);
    updateFormat(selected);
  };

  const handlePostfixChange = (selected: SelectOption) => {
    setLicenseValue('licensePostfix', selected);
    updateFormat(selected);
  };

  const updateFormat = (selected: SelectOption) => {
    const format = stateFormats?.find(
      (item: any) =>
        item.prefix === selected?.value || item.postfix === selected?.value
    );

    if (format) {
      setSelectedFormat(format);
      setLicenseValue('licenseFormat', format);
      triggerLicense('licenseNo');
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {}}
      className="w-[400px]"
      hideCloseButton={true}
    >
      <div className="pt-2 px-4 pb-4">
        <div className="text-center">
          <h5 className="text-black text-xl font-bold">
            Update License Number
          </h5>
          <p className="mt-2 text-sm text-gray-700">
            Your access to the app has been denied because your license number
            was rejected by the Admin. Please update your correct license number
            for uninterrupted app usage.
          </p>
        </div>

        {!formatsLoaded ? (
          <div className="mt-3 text-center">
            <p>Loading license formats...</p>
          </div>
        ) : (
          <form
            onSubmit={handleLicenseSubmit((data) => {
              onSubmitLicense(data);
            })}
            className="flex flex-col mt-3"
          >
            <div className="flex gap-x-4">
              <div className="relative mt-6 w-full">
                <div
                  className={`flex ${prefixOptions.length > 0 || postfixOptions.length > 0 ? 'gap-x-2' : ''}`}
                >
                  <label
                    className={clsx(
                      'text-sm font-medium text-left absolute -top-5 text-gray-700'
                    )}
                  >
                    License Number
                  </label>
                  {prefixOptions.length > 0 && (
                    <div className="w-[142px] relative">
                      <Controller
                        name="licensePrefix"
                        control={licenseControl}
                        render={({ field }) => (
                          <InputSelect
                            label={true}
                            field={{
                              ...field,
                              onChange: handlePrefixChange,
                              value: watchLicense('licensePrefix'),
                            }}
                            placeholder="Prefix"
                            className="relative"
                            smallDropdown={true}
                            errorMessage={
                              licenseErrors.licensePrefix?.message
                                ? String(licenseErrors.licensePrefix.message)
                                : undefined
                            }
                            options={prefixOptions}
                          />
                        )}
                      />
                    </div>
                  )}
                  <div
                    className={
                      prefixOptions.length > 0 || postfixOptions.length > 0
                        ? 'w-full'
                        : 'w-full'
                    }
                  >
                    <Controller
                      name="licenseNo"
                      control={licenseControl}
                      render={({ field }) => (
                        <InputField
                          label={true}
                          field={field}
                          placeholder="Enter your license number"
                          errorMessage={
                            licenseErrors.licenseNo?.message
                              ? String(licenseErrors.licenseNo.message)
                              : undefined
                          }
                        />
                      )}
                    />
                  </div>
                  {postfixOptions.length > 0 && (
                    <div className="w-[142px] relative">
                      <Controller
                        name="licensePostfix"
                        control={licenseControl}
                        render={({ field }) => (
                          <InputSelect
                            label={true}
                            field={{
                              ...field,
                              onChange: handlePostfixChange,
                              value: watchLicense('licensePostfix'),
                            }}
                            placeholder="Postfix"
                            className="relative"
                            smallDropdown={true}
                            errorMessage={
                              licenseErrors.licensePostfix?.message
                                ? String(licenseErrors.licensePostfix.message)
                                : undefined
                            }
                            options={postfixOptions}
                          />
                        )}
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-4 flex justify-center w-full">
              <Button
                loading={loading}
                text="Submit"
                type="submit"
                className="w-full"
              />
            </div>
          </form>
        )}
      </div>
    </Modal>
  );
};

export default LicenseCheck;
