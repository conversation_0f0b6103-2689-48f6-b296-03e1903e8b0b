import ProfileLayout from '@Components/Layout/ProfileLayout';
import {
  <PERSON>readcrumb,
  Button,
  DataTable,
  InputField,
  Modal,
} from '@Components/UI';
import ViewMore from '@Components/UI/VIewMoreText';
import { ColumnDef, SortingState } from '@tanstack/react-table';
import React, { useEffect, useState } from 'react';
import ReactSwitch from 'react-switch';
import ContentLayout from '@Components/Layout/ContentLayout';
import { AddIcon, CloseIcon, Loader, SearchIcon } from '@Icons';
import AddMembers from '../Enterprise/AddMembers';
import ActionButtons from '@Components/Common/ActionButtons';
import { useGetSubscriptionMember } from '@Query/Hooks/useMembers';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useDeleteSubUser } from '@Query/Hooks/useEnterprise';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { set } from 'react-hook-form';
import { useLocation } from 'react-router';
import { useDebounce } from '@Hooks/useDebouce';
import { useSelector } from 'react-redux';

type SearchableColumnDef<T> = ColumnDef<T> & {
  search?: boolean;
  vKey?: string;
};

interface LicenseFormat {
  format_id?: number;
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
}

interface StateData {
  id: number;
  name: string;
  license_formats: LicenseFormat[];
}

interface SelectOption {
  label: string;
  value: string;
  [key: string]: unknown;
}

interface User {
  orgId: number;
  email: string;
  first_name: string;
  last_name: string;
  mobile_no: string;
  user_id: number;
  status: boolean;
  license: string;
  state?: SelectOption & { license_formats?: LicenseFormat[] };
  license_format?: {
    prefix: string | null;
    postfix: string | null;
    min_number: number;
    max_number: number;
  };
  statesList?: StateData[];
  country_code: any;
  id?: number;
}

const Members = () => {
  const api = new Api();
  const { addToast } = useToast();
  const location = useLocation();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state?.UserControle?.user);

  const [openViewAccountModal, setOpenViewAccountModal] = useState(false);
  const [openDeleteAccountModal, setOpenDeleteAccountModal] = useState(false);
  const [openEditAccountModal, setOpenEditAccountModal] = useState(false);
  const [numberOfMembers, setNumberOfMembers] = useState<number | undefined>(1);
  const [isPurchaseOpen, setIsPurchaseOpen] = useState<boolean>(false);
  const [isPurchaseLoading, setIsPurchaseLoading] = useState<boolean>(false);
  const [numberOfMemberError, setNumberOfMemberError] =
    useState<boolean>(false);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const columns: SearchableColumnDef<User>[] = [
    {
      header: 'Name',
      accessorKey: 'first_name',
      enableSorting: true,
      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore
              text={`${row?.first_name} ${row?.last_name}`}
              maxLength={30}
            />
          </div>
        );
      },
    },
    {
      header: 'Email',
      accessorKey: 'email',
      enableSorting: true,

      cell: (info) => {
        const row = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            <ViewMore text={row?.email} maxLength={90} />
          </div>
        );
      },
    },
    {
      header: 'Phone number',
      accessorKey: 'mobile_no',
      enableSorting: false,
      cell: (info) => {
        return (
          <text>
            {info?.row?.original?.country_code} {info?.row?.original?.mobile_no}
          </text>
        );
      },
    },
    {
      header: 'Number of Uploads Used',
      accessorKey: 'uploaded_count',
      enableSorting: false,
      cell: (info) => info.getValue() ?? 0,
    },
    {
      header: 'Action',
      accessorKey: 'action',
      enableSorting: false,
      cell: (info) => (
        <ActionButtons
          deleteIcon
          editIcon
          viewIcon
          onView={() => {
            setSelectedRow({ ...info.row.original });
            setOpenViewAccountModal(true);
          }}
          onDelete={() => {
            setSelectedRow({ ...info.row.original });
            setOpenDeleteAccountModal(true);
          }}
          onEdit={() => {
            setSelectedRow({ ...info.row.original });
            setOpenEditAccountModal(true);
          }}
        />
      ),
    },
  ];

  const breadcrumbItems = [
    { label: 'Home', link: '/' },
    { label: 'Members Management', link: '/members' },
  ];

  const [openAddAccountModal, setOpenAddAccountModal] = useState(false);
  const [selectedRow, setSelectedRow] = useState<User | null>();
  const [invoiceId, setInvoiceId] = useState<string | null>(null);
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'id', desc: true },
  ]);
  const [searchText, setSearchText] = useState<string>('');

  const {
    data: membersDataList,
    isError: membersDataListError,
    isLoading: membersDataListLoading,
    refetch: refetchMembersDataList,
    isFetching: membersDataListFetching,
    error: membersDataListErrorMessage,
  } = useGetSubscriptionMember({
    ordering:
      sorting.length > 0
        ? `${sorting[0]?.desc === true ? '-' + sorting[0]?.id : sorting[0]?.id}`
        : 'id',
    page: currentPage,
    page_size: pageSize,
    search: searchText,
    isSearchable: searchText.length > 0,
  });
  const currentData = membersDataList?.data?.data;

  const {
    mutate: deleteUser,
    error: deleteUserError,
    isError: isErrorDeleteUser,
    isSuccess: isSuccessDeleteUser,
    data: deleteUserData,
    isLoading: isLoadingDeleteUser,
  } = useDeleteSubUser();

  useEffect(() => {
    if (searchText.length > 0) {
      setCurrentPage(1);
    }
  }, [searchText]);

  const debouncedSearchText = useDebounce(searchText, 500); // 500ms debounce

  useEffect(() => {
    refetchMembersDataList();
  }, [debouncedSearchText]);

  useEffect(() => {
    refetchMembersDataList();
  }, [sorting, pageSize, currentPage]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const paymentStatus = searchParams.get('payment_status');

    if (paymentStatus === 'success') {
      addToast('success', 'Payment successful!');
    } else if (paymentStatus === 'failed') {
      addToast('error', 'Payment failed!');
    }
  }, [location, addToast]);

  useEffect(() => {
    if (membersDataListError) {
      addToast('error', membersDataListErrorMessage as string);
    }
    if (isErrorDeleteUser) {
      addToast('error', deleteUserError as string);
    }
  }, [
    membersDataListError,
    membersDataListErrorMessage,
    addToast,
    isErrorDeleteUser,
    deleteUserError,
  ]);

  useEffect(() => {
    if (isSuccessDeleteUser) {
      setSelectedRow(null);
      addToast('success', deleteUserData?.data?.message);
      refetchMembersDataList();
      setOpenDeleteAccountModal(false);
    }
  }, [isSuccessDeleteUser]);

  const isPurchaseRequired =
    !currentData?.free_user_remaining_count &&
    !currentData?.purchased_user_remaining_count;

  const purchaseUser = async () => {
    try {
      if (!numberOfMembers) {
        setNumberOfMemberError(true);
        return;
      }
      setIsPurchaseLoading(true);
      const response = await api.post(API_PATHS.PURCHASE_SUB_USER, {
        data: {
          product_id: userData?.purchase_user_product_id,
          quantity: numberOfMembers,
        },
      });
      if (response?.data?.status) {
        if (response?.data?.data?.invoice_id) {
          // setIsPurchaseOpen(false);
          setInvoiceId(response?.data?.data?.invoice_id);
          window.open(response?.data?.data?.checkout_url, '_blank');
          const pollInterval = setInterval(async () => {
            try {
              const statusResponse = await api.post(
                API_PATHS.CHECK_INVOICE_STATUS,
                {
                  data: {
                    invoice_id: response?.data?.data?.invoice_id,
                  },
                }
              );

              const invoiceStatus = statusResponse?.data?.data?.invoice_status;

              if (invoiceStatus === 'paid' || invoiceStatus === 'failed') {
                clearInterval(pollInterval);

                // Handle the final status
                if (invoiceStatus === 'paid') {
                  // Handle successful payment
                  setTimeout(() => {
                    addToast('success', 'Payment successful');
                    setIsPurchaseOpen(false);
                    setInvoiceId(null);
                    refetchMembersDataList();
                  }, 3500);
                } else {
                  // Handle failed payment
                  addToast('error', 'Payment failed');
                  setIsPurchaseOpen(false);
                  setInvoiceId(null);
                }
              }
            } catch (error) {
              addToast('error', error as string);
              setIsPurchaseOpen(false);
              setInvoiceId(null);
              clearInterval(pollInterval);
            }
          }, 5000); // Poll every 5 seconds

          // Optional: Clear interval after a maximum time (e.g., 10 minutes)
          setTimeout(() => {
            clearInterval(pollInterval);
          }, 600000); // 10 minutes timeout
        } else {
          setIsPurchaseOpen(false);
          window.location.href = response?.data?.data?.checkout_url;
        }
      } else {
        addToast('error', response?.data?.message as string);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsPurchaseLoading(false);
      setNumberOfMembers(1);
    }
  };

  const onPageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const onPageSizeChange = (newSize: number) => {
    setPageSize(newSize);
    setCurrentPage(1);
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Members</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </div>
      <ContentLayout
        headerRightSideChildren={
          <div className="w-4xl flex items-center justify-end gap-5">
            <div className="w-80">
              <InputField
                placeholder="Search by name, email, phone"
                inputContainerClassName="max-h-[46px] w-full"
                className="p-0"
                containerClassName="p-0"
                onChange={(i) => setSearchText(i.target.value)}
                rightIcon={<SearchIcon height={18} width={18} />}
              />
            </div>

            <div className="flex gap-5">
              <Button
                text={
                  <div className="flex gap-2 px-2 items-center justify-center text-white w-max">
                    <AddIcon height={18} width={18} fill="#fff" />{' '}
                    {isPurchaseRequired
                      ? 'Purchase additional member'
                      : 'Add Member'}
                  </div>
                }
                onClick={() => {
                  if (isPurchaseRequired) {
                    setIsPurchaseOpen(true);
                  } else setOpenAddAccountModal(true);
                }}
                disabled={membersDataListLoading}
              />
            </div>
          </div>
        }
      >
        <DataTable
          data={currentData?.list}
          columns={columns}
          loading={membersDataListLoading}
          sorting={sorting}
          setSorting={setSorting}
          totalPages={currentData?.total_pages}
          currentPage={currentPage}
          onPageChange={onPageChange}
          pageSize={pageSize}
          setPageSize={(num) => {
            setPageSize(num);
            setCurrentPage(1);
          }}
          pageSizeOptions={[10, 20, 30, 50]}
          onPageSizeChange={onPageSizeChange}
          isPagination={true}
        />

        <Modal
          isOpen={openViewAccountModal}
          size="xxxl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">View Member</h3>
            </div>
          }
          onClose={() => {
            setOpenViewAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-x-10 gap-y-6 rounded-md">
              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">First Name</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.first_name}
                </p>
              </div>
              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">Last Name</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.last_name}
                </p>
              </div>

              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">Email</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.email}
                </p>
              </div>
              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">Phone Number</p>
                <p className="text-gray-800 font-normal break-words">
                  {selectedRow?.country_code} {selectedRow?.mobile_no}
                </p>
              </div>

              <div className="break-words w-full">
                <p className="text-gray-700 font-bold">
                  Number Of Uploads used
                </p>
                <p className="text-gray-800 font-normal break-words">0</p>
              </div>
            </div>
          }
        />

        <Modal
          isOpen={openDeleteAccountModal}
          onClose={() => {
            setOpenDeleteAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <div className="flex flex-col items-center gap-[36px] ">
              <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                <CloseIcon width={18} height={18} fill="#FF3B30" />
              </div>
              <div className="text-black font-bold text-center w-full">
                Are you sure you want to delete this member? This action cannot
                be undone.
              </div>
              <div className=" w-full flex justify-center gap-6">
                <Button
                  loading={isLoadingDeleteUser}
                  text="Delete"
                  variant="other"
                  className="h-[50px] w-[50px] border border-[#FF0000]
            text-[#FF0000] bg-transparent hover:border-[#FF0000]"
                  onClick={() =>
                    deleteUser({
                      userId: Number(selectedRow?.id),
                    })
                  }
                />
                <Button
                  text="Cancel"
                  variant="outline"
                  disabled={isLoadingDeleteUser}
                  onClick={() => {
                    setOpenDeleteAccountModal(false);
                    setSelectedRow(null);
                  }}
                />
              </div>
            </div>
          }
        />

        <Modal
          isOpen={openAddAccountModal}
          size="none"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Add Member</h3>
            </div>
          }
          onClose={() => {
            setOpenAddAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddMembers
              type="sub-user"
              handleClose={() => {
                setOpenAddAccountModal(false);
              }}
              handleSuccess={() => {
                setOpenAddAccountModal(false);
                refetchMembersDataList();
              }}
            />
          }
        />

        <Modal
          isOpen={openEditAccountModal}
          size="none"
          className="max-w-5xl"
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Edit Member</h3>
            </div>
          }
          onClose={() => {
            setOpenEditAccountModal(false);
            setSelectedRow(null);
          }}
          children={
            <AddMembers
              isEdit
              type="sub-user"
              handleSuccess={() => {
                setOpenEditAccountModal(false);
                refetchMembersDataList();
              }}
              userData={
                selectedRow ?? {
                  email: '',
                  first_name: '',
                  last_name: '',
                  mobile_no: '',
                  license: '',
                  id: 0,
                  country_code: '',
                  statesList: [],
                  state: { label: '', value: '' },
                  user_id: 0,
                  license_format: {
                    prefix: null,
                    postfix: null,
                    min_number: 0,
                    max_number: 0,
                  },
                }
              }
            />
          }
        />

        <Modal
          header="Add Member"
          isOpen={isPurchaseOpen}
          onClose={() => {
            setIsPurchaseOpen(false);
            setNumberOfMembers(1);
          }}
          size={'xl'}
          hideCloseButton={!!invoiceId}
          outsideClickClose={!invoiceId}
        >
          {invoiceId ? (
            <div className="flex w-full justify-center items-center flex-col gap-y-4 text-primary-light">
              <Loader height={80} width={80} fill="#00d9b1" stroke="#ebfffb" />
              <div className="text-xl py-6 text-slate-600 animate-left-to-right-pulse">
                Your payment is processing....
              </div>
            </div>
          ) : (
            <div className="p-4 flex flex-col gap-y-4">
              <div>
                You have exceeded your free number of members. Please buy a
                subscription plan to add more members at{' '}
                <span className="text-lg text-primary-100 font-bold">
                  {userData?.additional_user_price}$
                </span>
                /month per member
              </div>
              <InputField
                type="number"
                name="numberOfMembers"
                label="Number of Members"
                value={numberOfMembers || ''}
                onChange={(e) => {
                  const value = e.target.value;

                  // If empty, set to 0
                  if (!value) {
                    setNumberOfMembers(0);
                    setNumberOfMemberError(true);
                    return;
                  }

                  const numValue = Number(value);

                  // Don't allow negative numbers
                  if (numValue < 0) {
                    return;
                  }

                  setNumberOfMembers(numValue);
                  setNumberOfMemberError(numValue === 0);
                }}
                min="1"
                errorMessage={
                  numberOfMemberError ? 'Number of members is required' : ''
                }
              />
              {numberOfMembers && numberOfMembers > 0 && (
                <div className="font-medium text-sm">
                  Total Amount:{' '}
                  <span className="text-lg text-primary-100 font-bold">
                    {numberOfMembers * userData?.additional_user_price}$
                  </span>
                  /month
                </div>
              )}
              <Button
                text="Purchase"
                onClick={purchaseUser}
                loading={isPurchaseLoading}
                disabled={isPurchaseLoading}
              />
            </div>
          )}
        </Modal>
      </ContentLayout>
    </div>
  );
};

export default Members;
