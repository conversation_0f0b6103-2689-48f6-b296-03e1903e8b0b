/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import clsx from 'clsx';
import { X } from 'lucide-react';

// Import custom components and hooks
import { Button, InputField, InputSelect, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useStateList } from '@Query/Hooks/useAuth';
import { useGetUserProfile } from '@Query/Hooks/useAdmin';
import ProfileLayout from '@Components/Layout/ProfileLayout';
import { Loader, UserCirclePlusIcon } from '@Icons';
// Import constants
import {
  COUNTRY_CODES,
  DEFAULT_COUNTRY,
  DEFAULT_COUNTRY_CODE,
} from '@Helpers/PhoneCountryCodes';
import { useUpdateProfile } from '@Query/Hooks/useProfile';
import queryClient from '@Helpers/QueryClient';
import CacheKeys from '@Helpers/CacheKeys';
import VerifyOtp from '../VerifyOtp';
import { useDispatch, useSelector } from 'react-redux';
import { setUserData } from '@Redux/SystemControl/UserControle';

// Type Definitions
interface SelectOption {
  label: string;
  value: string;
  [key: string]: any;
}

interface LicenseFormat {
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
  format_id?: string;
}

interface StateData {
  id: number;
  name: string;
  license_formats: LicenseFormat[];
}

interface RegisterFormValues {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  countryCode: SelectOption;
  country: SelectOption;
  state: SelectOption & { license_formats?: LicenseFormat[] };
  licenseNo: string;
  licensePrefix: SelectOption | null;
  licensePostfix: SelectOption | null;
  licenseFormat: LicenseFormat | null;
}

const Profile: React.FC = () => {
  const { addToast } = useToast();
  const dispatch = useDispatch();
  const userData = useSelector((state: any) => state?.UserControle?.user);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [editToken, setEditToken] = useState<string>('');
  const [currentImage, setCurrentImage] = useState<string>('');
  const [profilePic, setProfilePic] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isMobileChanged, setIsMobileChanged] = useState(false);
  const [isImageRemoved, setIsImageRemoved] = useState(false);
  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const { data: stateListData, isLoading: stateListLoading } = useStateList();
  // const [licenseDataSetFromProfile, setLicenseDataSetFromProfile] =
  //   useState(false);

  const {
    data: viewProfileData,
    isError: isErrorInViewProfile,
    refetch: refetchProfileData,
    isLoading: isViewProfileLoading,
    error: errorOfViewProfile,
    isSuccess: isSuccessViewProfile,
  } = useGetUserProfile();

  const { mutate: updateUser, isLoading: isUpdateUserLoading } =
    useUpdateProfile({
      onSuccess: (data) => {
        setIsEdit(false);
        setIsImageRemoved(false);
        setSelectedFile(null);
        setIsImageUpdated(false);
        setPreviewUrl(null);
        if (isImageRemoved) {
          setCurrentImage('');
        }
        queryClient.invalidateQueries(CacheKeys.profileData);
        if (data?.data?.data?.is_mobile_updated) {
          setEditToken(data?.data?.data?.verify_token);
          setIsMobileChanged(true);
        }
        addToast('success', data?.data?.message);
      },
      onError: (error) => {
        addToast('error', error);
      },
    });

  // Validation Schema
  const schema = yup.object().shape({
    firstName: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('First name is required')
      .min(3, 'Name must have at least 3 characters')
      .max(50, 'Name must have at most 50 characters'),
    lastName: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Last name is required')
      .min(1, 'Name must have at least 1 character')
      .max(50, 'Name must have at most 50 characters'),
    email: yup
      .string()
      .required('Email is required')
      .matches(
        /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        'Please enter a valid email'
      ),
    phoneNumber: yup
      .string()
      .matches(/^[1-9][0-9]{7,11}$/, 'Enter a valid mobile number')
      .required('Mobile number is required'),
    countryCode: yup.mixed().required('Required'),
    country: yup.mixed().required('Country is required'),
    state: yup.mixed().required('State is required'),
    // licenseNo: yup
    //   .string()
    //   .trim('Cannot include leading and trailing spaces')
    //   .strict(true)
    //   .when(['state', 'licenseFormat'], {
    //     is: (state: any, licenseFormat: any) => state && licenseFormat,
    //     then: (schema) =>
    //       schema
    //         .required('License number is required')
    //         .matches(/^\d+$/, 'License number must contain only numbers')
    //         .test('length', 'Invalid license number length', function (value) {
    //           const { licenseFormat } = this.parent;
    //           if (!licenseFormat) return true;
    //           const { min_number, max_number } = licenseFormat;

    //           if (!value)
    //             return this.createError({
    //               message: 'License number is required',
    //             });

    //           if (value.length > max_number || value.length < min_number) {
    //             return this.createError({
    //               message: `License number must be exactly ${min_number === max_number ? min_number : `between ${min_number} and ${max_number}`} digits`,
    //             });
    //           }

    //           return true;
    //         }),
    //     otherwise: (schema) => schema.nullable(),
    //   }),
    // licensePrefix: yup.mixed().when('state', {
    //   is: (state: any) =>
    //     state && state.license_formats?.some((format: any) => format.prefix),
    //   then: (schema) => schema.required('License prefix is required'),
    //   otherwise: (schema) => schema.nullable(),
    // }),
    // licensePostfix: yup.mixed().when('state', {
    //   is: (state: any) =>
    //     state && state.license_formats?.some((format: any) => format.postfix),
    //   then: (schema) => schema.required('License postfix is required'),
    //   otherwise: (schema) => schema.nullable(),
    // }),
    // licenseFormat: yup.mixed().nullable(),
  });

  const {
    control,
    handleSubmit,
    clearErrors,
    formState: { errors },
    setValue,
    watch,
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      countryCode: DEFAULT_COUNTRY_CODE as unknown as SelectOption,
      country: DEFAULT_COUNTRY as unknown as SelectOption,
      state: null as unknown as RegisterFormValues['state'],
      licenseNo: '',
      licensePrefix: null,
      licensePostfix: null,
      licenseFormat: null,
    },
    mode: 'onChange',
  });

  // Watch the state value to determine if we need to show license fields
  const stateValue = watch('state');

  // Function to determine if selected state has prefixes
  // const hasPrefixes = () => {
  //   if (!stateValue || !stateValue.license_formats) return false;
  //   return stateValue.license_formats.some(
  //     (format: LicenseFormat) => format.prefix !== null
  //   );
  // };

  // Function to determine if selected state has postfixes
  // const hasPostfixes = () => {
  //   if (!stateValue || !stateValue.license_formats) return false;
  //   return stateValue.license_formats.some(
  //     (format: LicenseFormat) => format.postfix !== null
  //   );
  // };

  // Get license prefix options from the selected state
  const getLicensePrefixOptions = () => {
    if (
      // !stateValue ||
      // !stateValue.license_formats ||
      !viewProfileData?.data.data?.prefix
    ) {
      return [];
    }

    // return stateValue.license_formats
    //   .filter((format) => format.prefix !== null)
    //   .map((format) => ({
    //     label: format.prefix || '',
    //     value: format.prefix || '',
    //   }));

    return [
      {
        label: viewProfileData?.data.data?.prefix || '',
        value: viewProfileData?.data.data?.prefix || '',
      },
    ];
  };

  // Get license postfix options from the selected state
  const getLicensePostfixOptions = () => {
    if (
      // !stateValue ||
      // !stateValue.license_formats ||
      !viewProfileData?.data.data?.postfix
    ) {
      return [];
    }

    // return stateValue.license_formats
    //   .filter((format) => format.postfix !== null)
    //   .map((format) => ({
    //     label: format.postfix || '',
    //     value: format.postfix || '',
    //   }));
    return [
      {
        label: viewProfileData?.data.data?.postfix || '',
        value: viewProfileData?.data.data?.postfix || '',
      },
    ];
  };

  const populateData = (isCancel = false) => {
    if (isSuccessViewProfile && viewProfileData?.data?.data) {
      if (viewProfileData?.data?.data?.profile_picture) {
        setCurrentImage(viewProfileData?.data?.data?.profile_picture);
        setProfilePic(viewProfileData?.data?.data?.profile_picture);
      } else {
        setCurrentImage('');
        setProfilePic('');
      }
      const profileData = viewProfileData.data.data;
      if (!isCancel) {
        dispatch(setUserData({ ...userData, ...profileData }));
      }
      // Set basic profile information
      setValue('firstName', profileData.first_name);
      setValue('lastName', profileData.last_name);
      setValue('email', profileData.email);
      setValue('countryCode', {
        value: profileData.country_code,
        label: profileData.country_code,
      });
      setValue('phoneNumber', profileData.mobile_no);
      setValue('country', {
        value: profileData?.country?.toString(),
        label: profileData.country_name,
      });

      // Set state with license formats if state list data is available
      if (stateListData?.data?.data) {
        const stateId = parseInt(profileData?.state?.toString(), 10);
        const state = stateListData.data.data.find(
          (s: StateData) => s.id === stateId
        );

        if (state) {
          setValue('state', {
            value: state.id.toString(),
            label: state.name,
            license_formats: state.license_formats,
          });

          // Set license information
          setValue('licenseNo', profileData.user_license?.toString() || '');

          // Set license format if available
          if (profileData.format_id) {
            // const format = state.license_formats.find(
            //   (f: LicenseFormat) => f.format_id === profileData.format_id
            // );

            // if (format) {
            //   setValue('licenseFormat', format);

            // Set prefix if it exists
            if (profileData.prefix) {
              setValue('licensePrefix', {
                value: profileData.prefix,
                label: profileData.prefix,
              });
            }

            // Set postfix if it exists
            if (profileData.postfix) {
              setValue('licensePostfix', {
                value: profileData.postfix,
                label: profileData.postfix,
              });
            }
            // }
          }
          // setLicenseDataSetFromProfile(true);
        }
      }
    }
  };

  // Populate form with user profile data
  useEffect(() => {
    populateData();
    if (isErrorInViewProfile) {
      addToast('error', errorOfViewProfile as string);
    }
  }, [
    viewProfileData,
    isSuccessViewProfile,
    isErrorInViewProfile,
    errorOfViewProfile,
    stateListData,
    setValue,
    addToast,
  ]);

  // useEffect(() => {
  //   if (licenseDataSetFromProfile) {
  //     return;
  //   }

  //   if (stateValue && stateValue.license_formats) {
  //     setValue('licenseNo', '');
  //     setValue('licensePrefix', null);
  //     setValue('licensePostfix', null);
  //     setValue('licenseFormat', null);
  //   }
  // }, [stateValue, licenseDataSetFromProfile, setValue]);

  const onSubmit = (data: RegisterFormValues) => {
    const formData = new FormData();

    const country = parseInt(data.country.value, 10);
    const state = parseInt(data.state.value, 10);

    formData.append('first_name', data.firstName);
    formData.append('last_name', data.lastName);
    formData.append('email', data.email);
    formData.append('mobile_no', data.phoneNumber);
    formData.append('country_code', data.countryCode.value);
    formData.append('country', country.toString());
    formData.append('state', state.toString());
    formData.append('is_profile_removed', String(isImageRemoved));
    formData.append('is_profile_updated', String(isImageUpdated));

    if (selectedFile) {
      formData.append('profile_picture', selectedFile);
    }
    updateUser(formData);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);

      if (currentImage) {
        setIsImageUpdated(true);
      } else if (isImageRemoved) {
        setIsImageUpdated(true);
        setIsImageRemoved(false);
      }
      setCurrentImage('');
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleCancel = () => {
    setIsEdit(false);
    setCurrentImage(profilePic);
    setSelectedFile(null);
    setIsImageRemoved(false);
    setIsImageUpdated(false);
    setPreviewUrl(null);
    setProfilePic('');
    populateData(true);
    clearErrors();
  };

  const onEditSuccess = () => {
    setIsMobileChanged(false);
    setEditToken('');
    queryClient.invalidateQueries(CacheKeys.profileData);
  };

  const clearSelection = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setSelectedFile(null);
    setPreviewUrl(null);
    if (currentImage) {
      setCurrentImage('');
      setIsImageRemoved(true);
    } else if (isImageUpdated) {
      setIsImageUpdated(false);
      setIsImageRemoved(true);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const getLicenseStatus = () => {
    const status = userData?.license_status || 'Pending';
    return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
  };

  const getLicenseStatusClass = () => {
    const status = getLicenseStatus().toLowerCase();
    if (status === 'pending') return 'text-blue-600';
    if (status === 'approved') return 'text-green-600';
    if (status === 'resubmit' || status === 'resubmitted')
      return 'text-amber-600';
    if (status === 'rejected') return 'text-red-600';
    return 'text-gray-600';
  };

  useEffect(() => {
    const handleRefresh = () => {
      refetchProfileData();
    };
    window.addEventListener('refreshProfileData', handleRefresh);
    return () => {
      window.removeEventListener('refreshProfileData', handleRefresh);
    };
  }, []);

  return (
    <ProfileLayout>
      <div>
        <div className="flex flex-col justify-between sticky top-0 bg-white z-20">
          <div
            className={clsx(
              'flex justify-between gap-1.5 p-6 px-10 pb-0 h-[40px]'
            )}
          >
            <div className="flex justify-between w-full">
              <h3 className="text-xl font-semibold">My Profile</h3>
              {!isEdit && (
                <Button
                  text="Edit"
                  type="button"
                  className="!w-[65px] h-[40px] !p-2"
                  onClick={() => setIsEdit(true)}
                  disabled={isViewProfileLoading}
                />
              )}
            </div>
          </div>
        </div>
        <div className="p-10 flex flex-col gap-4 ">
          <div className="max-w-[900px]">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="flex flex-col items-center">
                {/* Circle container with profile image or icon */}
                <div
                  className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center relative cursor-pointer overflow-hidden"
                  onClick={handleButtonClick}
                >
                  {previewUrl || currentImage ? (
                    <img
                      src={previewUrl || currentImage}
                      alt="Profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <UserCirclePlusIcon />
                  )}

                  <input
                    type="file"
                    ref={fileInputRef}
                    disabled={!isEdit}
                    className="hidden"
                    accept=".jpg,.jpeg,.png"
                    onChange={handleFileSelect}
                  />
                </div>

                {(selectedFile || currentImage) && (
                  <div className="mt-3 flex items-center bg-gray-100  rounded-md gap-4">
                    {selectedFile && isEdit ? (
                      <>
                        <span className="text-gray-700 text-sm flex-grow truncate px-3 py-2">
                          {selectedFile.name}
                        </span>
                        <button
                          onClick={clearSelection}
                          className="text-gray-500 hover:text-red-500 px-3 py-2"
                        >
                          <X size={16} />
                        </button>
                      </>
                    ) : (
                      currentImage &&
                      isEdit && (
                        <button
                          onClick={clearSelection}
                          className="text-gray-500 hover:text-red-500 h-[30px] px-3 text-[13px] hover:cursor-pointer"
                        >
                          Remove
                        </button>
                      )
                    )}
                  </div>
                )}
              </div>

              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="First Name"
                      field={field}
                      placeholder="First Name"
                      errorMessage={errors.firstName?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Last Name"
                      field={field}
                      placeholder="Last Name"
                      errorMessage={errors.lastName?.message}
                      disabled={!isEdit}
                    />
                  )}
                />
              </div>
              <div className="flex gap-x-4 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <InputField
                        label="Email"
                        field={field}
                        placeholder="Enter your email"
                        errorMessage={errors.email?.message}
                        disabled={!isEdit}
                      />
                    )}
                  />
                </div>

                <div className="w-1/2">
                  <div className="relative mt-6">
                    <div className="flex gap-x-2">
                      <label
                        className={clsx(
                          'text-sm font-medium text-left absolute -top-5 text-gray-700'
                        )}
                      >
                        Mobile Number
                      </label>

                      <div className="w-[108px]">
                        <Controller
                          name="countryCode"
                          control={control}
                          render={({ field }) => (
                            <InputSelect
                              label={true}
                              field={field}
                              disabled={!isEdit}
                              placeholder="+61"
                              errorMessage={
                                errors.countryCode?.message
                                  ? String(errors.countryCode.message)
                                  : undefined
                              }
                              options={COUNTRY_CODES}
                            />
                          )}
                        />
                      </div>

                      <div className="w-full">
                        <Controller
                          name="phoneNumber"
                          control={control}
                          render={({ field }) => (
                            <InputField
                              label={true}
                              field={field}
                              placeholder="Enter your mobile number"
                              errorMessage={errors.phoneNumber?.message}
                              disabled={!isEdit}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex gap-x-4 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="country"
                    control={control}
                    render={({ field }) => (
                      <InputSelect
                        label={'Country'}
                        field={field}
                        placeholder="Select country"
                        disabled={!isEdit}
                        errorMessage={
                          errors.country?.message
                            ? String(errors.country.message)
                            : undefined
                        }
                        options={[{ value: '+61', label: 'Australia' }]}
                      />
                    )}
                  />
                </div>

                <div className="w-1/2">
                  <Controller
                    name="state"
                    control={control}
                    render={({ field }) => (
                      <InputSelect
                        label={'State'}
                        field={field}
                        placeholder="Select your state"
                        disabled={!isEdit}
                        errorMessage={
                          errors.state?.message
                            ? String(errors.state.message)
                            : undefined
                        }
                        options={
                          stateListData?.data?.data?.map(
                            (state: StateData) => ({
                              label: state.name,
                              value: state.id.toString(),
                              license_formats: state.license_formats,
                            })
                          ) || []
                        }
                        isLoading={stateListLoading}
                      />
                    )}
                  />
                </div>
              </div>
              <div className="flex gap-x-4 pt-8">
                {stateValue &&
                  stateValue.license_formats &&
                  stateValue.license_formats.length > 0 && (
                    <div className="w-1/2">
                      <div className="relative mt-6">
                        <div
                          className={`flex ${viewProfileData?.data.data?.prefix || viewProfileData?.data.data?.postfix ? 'gap-x-2' : ''}`}
                        >
                          <label
                            className={clsx(
                              'text-sm font-medium text-left absolute -top-6   text-gray-700 flex gap-3 h-auto '
                            )}
                          >
                            License Number{' '}
                            <p
                              className={`font-medium ${getLicenseStatusClass()}`}
                            >
                              {' '}
                              {getLicenseStatus()}{' '}
                            </p>
                          </label>

                          {viewProfileData?.data.data?.prefix && (
                            <div className="w-[142px]">
                              <Controller
                                name="licensePrefix"
                                control={control}
                                render={({ field }) => (
                                  <InputSelect
                                    label={true}
                                    field={field}
                                    placeholder="Prefix"
                                    errorMessage={
                                      errors.licensePrefix?.message
                                        ? String(errors.licensePrefix.message)
                                        : undefined
                                    }
                                    options={getLicensePrefixOptions()}
                                    disabled
                                  />
                                )}
                              />
                            </div>
                          )}

                          <div
                            className={
                              viewProfileData?.data.data?.prefix ||
                              viewProfileData?.data.data?.postfix
                                ? 'w-full'
                                : 'w-full'
                            }
                          >
                            <Controller
                              name="licenseNo"
                              control={control}
                              render={({ field }) => (
                                <InputField
                                  label={true}
                                  field={field}
                                  placeholder="Enter your license number"
                                  errorMessage={errors.licenseNo?.message}
                                  disabled
                                />
                              )}
                            />
                          </div>
                          {viewProfileData?.data.data?.postfix && (
                            <div className="w-[142px]">
                              <Controller
                                name="licensePostfix"
                                control={control}
                                render={({ field }) => (
                                  <InputSelect
                                    label={true}
                                    field={field}
                                    placeholder="Postfix"
                                    errorMessage={
                                      errors.licensePostfix?.message
                                        ? String(errors.licensePostfix.message)
                                        : undefined
                                    }
                                    options={getLicensePostfixOptions()}
                                    disabled
                                  />
                                )}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
              </div>
              {isEdit && (
                <div className="mt-8 flex justify-center gap-10">
                  <div className="min-w-[150px]">
                    <Button
                      text="Cancel"
                      type="button"
                      className="w-full"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isUpdateUserLoading}
                    />
                  </div>
                  <div className="min-w-[150px]">
                    <Button
                      text="Save"
                      type="submit"
                      className="w-full"
                      loading={isUpdateUserLoading}
                    />
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
      {isMobileChanged && (
        <Modal
          isOpen={isMobileChanged}
          size="md"
          outsideClickClose={false}
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Verify OTP</h3>
            </div>
          }
          onClose={() => {
            setIsMobileChanged(false);
            queryClient.invalidateQueries(CacheKeys.profileData);
          }}
          children={
            <VerifyOtp
              isEdit={true}
              onEditSuccess={onEditSuccess}
              editToken={editToken}
            />
          }
        />
      )}
    </ProfileLayout>
  );
};

export default Profile;
