import { useEffect, useRef, useState, useCallback } from 'react';
import { <PERSON><PERSON>, Modal, InputField, InputSelect } from '@Components/UI';
import { useForm, Controller, ControllerRenderProps } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import clsx from 'clsx';
import { useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router';
import { PATHS } from '@Config/Path.Config';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { setUserData } from '@Redux/SystemControl/UserControle';
import { useStateList } from '@Query/Hooks/useAuth';
import { requestForToken } from '@Config/firebaseConfig';
import EnterpriseRegisterForm from './EnterpriseRegisterForm';
import Api from '@Helpers/Api';
import { Loader } from '@Icons';
import parse from 'html-react-parser';

import { API_PATHS } from '@Helpers/Constants';

import {
  COUNTRY_CODES,
  DEFAULT_COUNTRY,
  DEFAULT_COUNTRY_CODE,
} from '@Helpers/PhoneCountryCodes';
import { c } from 'node_modules/framer-motion/dist/types.d-6pKw1mTI';

interface LicenseFormat {
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
  format_id: number;
}

interface InputFieldProps {
  label?: boolean;
  field: {
    onFocus?: () => void;
    onChange: (e: any) => void;
    onBlur: () => void;
    value: string;
    disabled?: boolean;
    name: string;
    ref: (instance: any) => void;
  };
  placeholder?: string;
  errorMessage?: string;
  helperText?: string;
}

interface StateData {
  id: number;
  name: string;
  license_formats: LicenseFormat[];
}

interface SelectOption {
  label: string;
  value: string;
  [key: string]: any;
}

const schema = yup.object().shape({
  firstName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('First name is required')
    .min(3, 'Name must have at least 3 characters')
    .max(50, 'Name must have at most 50 characters')
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed'),
  lastName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('Last name is required')
    .min(1, 'Name must have atleast 1 characters')
    .max(50, 'Name must have atmost 50 characters')
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed'),

  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email'
    ),
  phoneNumber: yup
    .string()
    .matches(/^[1-9][0-9]{7,11}$/, 'Enter a valid mobile number')
    .required('Mobile number is required'),
  countryCode: yup.mixed().required('Required'),
  country: yup.mixed().required('Country is required'),
  state: yup.mixed().required('State is required'),
  licenseNo: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .when(['state', 'licenseFormat'], {
      is: (state: any, licenseFormat: any) => state && licenseFormat,
      then: (schema) =>
        schema
          .required('License number is required')
          .matches(/^\d+$/, 'License number must contain only numbers')
          .test('length', 'Invalid license number length', function (value) {
            const { licenseFormat } = this.parent;

            if (!licenseFormat) return true;

            const { min_number, max_number } = licenseFormat;

            // Return false immediately if empty to show required message
            if (!value)
              return this.createError({
                message: 'License number is required',
              });

            // Check if value exceeds the maximum length
            if (value.length > max_number) {
              return this.createError({
                message: `License number must be exactly ${min_number === max_number ? min_number : `between ${min_number} and ${max_number}`} digits`,
              });
            }

            // Check if value is too short but user might still be typing
            if (value.length < min_number) {
              return this.createError({
                message: `License number must be exactly ${min_number === max_number ? min_number : `between ${min_number} and ${max_number}`} digits`,
              });
            }

            return true;
          }),
      otherwise: (schema) => schema.nullable(),
    }),
  licensePrefix: yup.mixed().when('state', {
    is: (state: any) =>
      state && state.license_formats?.some((format: any) => format.prefix),
    then: (schema) => schema.required('License prefix is required'),
    otherwise: (schema) => schema.nullable(),
  }),
  licensePostfix: yup.mixed().when('state', {
    is: (state: any) =>
      state && state.license_formats?.some((format: any) => format.postfix),
    then: (schema) => schema.required('License postfix is required'),
    otherwise: (schema) => schema.nullable(),
  }),
  licenseFormat: yup.mixed().nullable(),
});

interface RegisterFormValues {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  countryCode: SelectOption;
  country: SelectOption;
  state: SelectOption & { license_formats?: LicenseFormat[] };
  licenseNo: string;
  licensePrefix: SelectOption | null;
  licensePostfix: SelectOption | null;
  licenseFormat: LicenseFormat | null;
}

interface RegisterRequestData {
  first_name: string;
  last_name: string;
  email: string;
  mobile: string;
  device_id: string | null;
  device_token: string | null;
  country_code: string;
  country: number;
  state: number;
  license: string;
  format_id: number;
}

const Register = (): JSX.Element => {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const dispatch = useDispatch();
  const api = useRef(new Api()).current;

  const [loading, setLoading] = useState<boolean>(false);
  const [deviceState, setDeviceState] = useState({
    deviceId: '',
    firebaseToken: '',
    isInitialized: false,
    notificationPermission: null as NotificationPermission | null,
  });

  // Add new state for terms modal and content
  const [isTermsModalOpen, setIsTermsModalOpen] = useState<boolean>(false);
  const [termsContent, setTermsContent] = useState<{
    title?: string;
    content?: string;
  } | null>(null);
  const [termsLoading, setTermsLoading] = useState<boolean>(false);
  const [isTNCAccepted, setIsTNCAccepted] = useState(false);

  const getStoredFormValues = () => {
    try {
      const storedValues = sessionStorage.getItem('registerFormValues');
      if (storedValues) {
        return JSON.parse(storedValues) as Partial<RegisterFormValues>;
      }
    } catch (error) {
      console.error('Error parsing stored form values:', error);
    }

    return {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      countryCode: DEFAULT_COUNTRY_CODE as unknown as SelectOption,
      country: DEFAULT_COUNTRY as unknown as SelectOption,
      state: null as unknown as RegisterFormValues['state'],
      licenseNo: '',
      licensePrefix: null,
      licensePostfix: null,
      licenseFormat: null,
    };
  };

  const initializeDevice = useCallback(async () => {
    const getDeviceId = () => {
      let storedDeviceId = localStorage.getItem('deviceId');
      if (!storedDeviceId) {
        storedDeviceId = uuidv4();
        localStorage.setItem('deviceId', storedDeviceId);
      }
      return storedDeviceId;
    };

    const checkNotificationPermission = async () => {
      if (!('Notification' in window)) {
        addToast('error', 'This browser does not support notifications');
        return false;
      }

      let permission = Notification.permission;
      if (permission === 'default') {
        try {
          permission = await Notification.requestPermission();
        } catch (error) {
          addToast('error', 'Failed to request notification permissions');
          return false;
        }
      }

      return permission === 'granted';
    };

    const deviceId = getDeviceId();
    const firebaseToken = (await requestForToken()) || '';
    const notificationPermission = await checkNotificationPermission();

    setDeviceState({
      deviceId,
      firebaseToken,
      isInitialized: notificationPermission,
      notificationPermission: Notification.permission,
    });
  }, [addToast]);

  useEffect(() => {
    initializeDevice();
  }, [initializeDevice]);

  const [selectedState, setSelectedState] = useState<StateData | null>(null);
  const [hasPrefixes, setHasPrefixes] = useState<boolean>(false);
  const [hasPostfixes, setHasPostfixes] = useState<boolean>(false);
  const [licenseError, setLicenseError] = useState<string | null>(null);
  const [licenseNoTouched, setLicenseNoTouched] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [previousStateId, setPreviousStateId] = useState<number | null>(null);

  const { data: stateListData, isLoading: stateListLoading } = useStateList();

  const storedValues = getStoredFormValues();

  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
    trigger,
    getValues,
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: storedValues,
    mode: 'onChange',
  });

  const formValues = watch();

  useEffect(() => {
    if (
      formValues.firstName ||
      formValues.lastName ||
      formValues.email ||
      formValues.phoneNumber ||
      formValues.licenseNo
    ) {
      try {
        sessionStorage.setItem(
          'registerFormValues',
          JSON.stringify(formValues)
        );
      } catch (error) {
        console.error('Error saving form values to session storage:', error);
      }
    }
  }, [formValues]);

  // Watch form fields
  const stateValue = watch('state');
  const licensePrefixValue = watch('licensePrefix');
  const licensePostfixValue = watch('licensePostfix');
  const licenseNoValue = watch('licenseNo');
  const licenseFormatValue = watch('licenseFormat');

  // Validate license number in real time
  useEffect(() => {
    const validateLicenseNo = () => {
      if (!licenseNoTouched || !licenseFormatValue) {
        setLicenseError(null);
        return;
      }

      const { min_number, max_number } = licenseFormatValue;

      if (!licenseNoValue) {
        setLicenseError('License number is required');
        return;
      }

      if (!/^\d+$/.test(licenseNoValue)) {
        setLicenseError('License number must contain only numbers');
        return;
      }

      if (licenseNoValue.length > max_number) {
        setLicenseError(
          `License number must be exactly ${min_number === max_number ? min_number : `between ${min_number} and ${max_number}`} digits`
        );
        return;
      }

      if (licenseNoValue.length < min_number) {
        setLicenseError(
          `License number must be exactly ${min_number === max_number ? min_number : `between ${min_number} and ${max_number}`} digits`
        );
        return;
      }

      setLicenseError(null);
    };

    validateLicenseNo();
  }, [licenseNoValue, licenseFormatValue, licenseNoTouched]);

  // Update selected state and reset license fields when state changes
  useEffect(() => {
    if (stateValue && stateListData?.data?.data) {
      const stateId = parseInt(stateValue.value, 10);
      const foundState = stateListData?.data?.data?.find(
        (state: StateData) => state.id === stateId
      );

      setSelectedState(foundState || null);

      // Check if state has license formats with prefixes and postfixes
      if (foundState && foundState.license_formats) {
        const hasPrefixFormats = foundState.license_formats.some(
          (format: LicenseFormat) => format.prefix !== null
        );
        const hasPostfixFormats = foundState.license_formats.some(
          (format: LicenseFormat) => format.postfix !== null
        );

        setHasPrefixes(hasPrefixFormats);
        setHasPostfixes(hasPostfixFormats);

        if (previousStateId !== stateId) {
          setValue('licensePrefix', null);
          setValue('licensePostfix', null);
          setValue('licenseNo', '');
          setValue('licenseFormat', null);
          setLicenseError(null);

          if (foundState.license_formats.length === 1) {
            const format = foundState.license_formats[0];
            if (format.prefix) {
              setValue('licensePrefix', {
                label: format.prefix,
                value: format.prefix,
              });
            }
            if (format.postfix) {
              setValue('licensePostfix', {
                label: format.postfix,
                value: format.postfix,
              });
            }
            setValue('licenseFormat', format);
          }

          setPreviousStateId(stateId);
        }
      } else {
        setHasPrefixes(false);
        setHasPostfixes(false);
      }
    } else {
      setSelectedState(null);
      setHasPrefixes(false);
      setHasPostfixes(false);
    }
  }, [stateValue, stateListData, setValue, previousStateId]);

  // Update license format when prefix or postfix changes
  useEffect(() => {
    if (selectedState && selectedState.license_formats) {
      let format: LicenseFormat | null = null;

      if (selectedState.license_formats.length === 1) {
        // If there's only one format, use it
        format = selectedState.license_formats[0];
      } else {
        // Find matching format based on prefix and/or postfix
        const prefixValue = licensePrefixValue?.value || null;
        const postfixValue = licensePostfixValue?.value || null;

        format =
          selectedState.license_formats.find(
            (f) =>
              (f.prefix === prefixValue ||
                (!hasPrefixes && f.prefix === null)) &&
              (f.postfix === postfixValue ||
                (!hasPostfixes && f.postfix === null))
          ) || null;
      }

      setValue('licenseFormat', format);

      if (format && licenseNoValue) {
        if (licenseNoValue.length > format.max_number) {
          setLicenseError(
            `License number must be exactly ${
              format.min_number === format.max_number
                ? format.min_number
                : `between ${format.min_number} and ${format.max_number}`
            } digits`
          );
        } else if (licenseNoValue.length < format.min_number) {
          setLicenseError(
            `License number must be exactly ${
              format.min_number === format.max_number
                ? format.min_number
                : `between ${format.min_number} and ${format.max_number}`
            } digits`
          );
        } else {
          setLicenseError(null);
        }
      }
    }
  }, [
    selectedState,
    licensePrefixValue,
    licensePostfixValue,
    hasPrefixes,
    hasPostfixes,
    setValue,
    licenseNoValue,
  ]);

  useEffect(() => {
    const fetchTermsAndCondition = async function () {
      try {
        const { data } = await api.get(API_PATHS.TERMS_AND_CONDITION);
        const termsAndConditionContent = data?.data;
        if (termsAndConditionContent) {
          setTermsContent(termsAndConditionContent);
        }
      } catch (error: unknown) {
        addToast('error', error as string);
      } finally {
        setTermsLoading(false);
      }
    };
    fetchTermsAndCondition();
  }, [addToast, api]);

  const onSubmit = useCallback(
    async (formData: RegisterFormValues): Promise<void> => {
      setLoading(true);
      const { deviceId, firebaseToken, isInitialized } = deviceState;

      // if (!isInitialized) {
      //   addToast('error', 'Please allow notifications to proceed');
      //   setLoading(false);
      //   return;
      // }

      if (formData.licenseFormat) {
        const { min_number, max_number } = formData.licenseFormat;

        if (
          !formData.licenseNo ||
          formData.licenseNo.length < min_number ||
          formData.licenseNo.length > max_number
        ) {
          setLicenseError(
            `License number must be exactly ${min_number === max_number ? min_number : `between ${min_number} and ${max_number}`} digits`
          );
          setLoading(false);
          return;
        }
      }

      const licenseNumber = formData.licenseNo;

      const formatId = formData.licenseFormat
        ? formData.licenseFormat.format_id
        : null;

      try {
        const { data } = await api.post(API_PATHS.REGISTER, {
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
            mobile: formData.phoneNumber,
            device_id: deviceId,
            device_token: firebaseToken,
            country_code: formData.countryCode.value,
            country: parseInt(formData.country.value, 10),
            state: parseInt(formData.state.value, 10),
            license: licenseNumber,
            format_id: formatId,
            is_terms_condition_accepted: true,
          },
        });

        const responseData = data;
        if (responseData) {
          addToast('success', responseData.message);
          setLoading(false);
          setIsTermsModalOpen(false);
          dispatch(setUserData(responseData?.data));
          navigate(PATHS.VERIFY_OTP);
        }
      } catch (error) {
        setLoading(false);
        setIsTermsModalOpen(false);
        setIsTNCAccepted(true);
        addToast('error', error as string);
      }
    },
    [deviceState, addToast, api, navigate, dispatch]
  );

  const getLicensePrefixOptions = () => {
    if (!selectedState || !selectedState.license_formats || !hasPrefixes) {
      return [];
    }

    const uniquePrefixes = new Set<string>();
    const options: SelectOption[] = [];

    for (const format of selectedState.license_formats) {
      if (format.prefix && !uniquePrefixes.has(format.prefix)) {
        uniquePrefixes.add(format.prefix);
        options.push({
          label: format.prefix,
          value: format.prefix,
        });
      }
    }

    return options;
  };

  const getLicensePostfixOptions = () => {
    if (!selectedState || !selectedState.license_formats || !hasPostfixes) {
      return [];
    }

    const uniquePostfixes = new Set<string>();
    const options: SelectOption[] = [];

    for (const format of selectedState.license_formats) {
      if (format.postfix && !uniquePostfixes.has(format.postfix)) {
        uniquePostfixes.add(format.postfix);
        options.push({
          label: format.postfix,
          value: format.postfix,
        });
      }
    }

    return options;
  };

  const handleLicenseNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    onChange: (value: string) => void
  ) => {
    const value = e.target.value.replace(/\D/g, '');
    const format = getValues('licenseFormat');

    if (format) {
      const truncatedValue = value.slice(0, format.max_number);
      onChange(truncatedValue);
    } else {
      onChange(value);
    }
  };

  const handleLicenseNumberFocus = () => {
    setLicenseNoTouched(true);
  };

  type ExtendedControllerRenderProps = ControllerRenderProps & {
    onFocus?: () => void;
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleTermsModalOpen = () => {
    setIsTermsModalOpen(true);
  };

  const handleTermsModalClose = () => {
    if (!loading) {
      setIsTermsModalOpen(false);
    }
  };

  const buttonClickHandler = async () => {
    const isValid = await trigger();

    if (isValid) {
      handleTermsModalOpen();
    }
  };

  return (
    <div>
      <Modal
        header="Enterprise Register"
        size="xl"
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        hideCloseButton={false}
      >
        <EnterpriseRegisterForm onClose={handleCloseModal} />
      </Modal>

      <form
        className="flex flex-col space-y-4 pt-2"
        onSubmit={handleSubmit(onSubmit)}
        autoComplete="off"
      >
        <Modal
          header={termsContent?.title || 'Terms and Conditions'}
          size="xl"
          isOpen={isTermsModalOpen}
          onClose={handleTermsModalClose}
          hideCloseButton={false}
        >
          <div className="p-4">
            <div
              className="reset-styles max-h-[500px] overflow-y-auto p-4 text-gray-700 leading-relaxed border border-gray-200 rounded-md"
              style={{ scrollBehavior: 'smooth' }}
            >
              {termsLoading ? (
                <div className="flex py-6 w-full justify-center items-center">
                  <Loader fill="#fff" height={36} width={36} />
                </div>
              ) : termsContent?.content ? (
                parse(termsContent.content)
              ) : (
                <p className="text-center">No content available</p>
              )}
            </div>
            <div className="flex justify-end gap-4 mt-6">
              <Button
                text="Cancel"
                variant="outline"
                onClick={handleTermsModalClose}
              />
              <Button text="Accept" type="submit" loading={loading} />
            </div>
          </div>
        </Modal>
        <div className="flex gap-x-4">
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <InputField
                label="First Name"
                field={field}
                placeholder="First Name"
                errorMessage={errors.firstName?.message}
                autoFocus
              />
            )}
          />
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <InputField
                label="Last Name"
                field={field}
                placeholder="Last Name"
                errorMessage={errors.lastName?.message}
              />
            )}
          />
        </div>
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <InputField
              label="Email"
              field={field}
              placeholder="Enter your email"
              errorMessage={errors.email?.message}
            />
          )}
        />
        <div className="relative mt-6">
          <div className="flex gap-x-2">
            <label
              className={clsx(
                'text-sm font-medium text-left absolute -top-5 text-gray-700'
              )}
            >
              Mobile Number
            </label>
            <div className="w-[108px]">
              <Controller
                name="countryCode"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    label={true}
                    field={field}
                    placeholder="+61"
                    errorMessage={
                      errors.countryCode?.message
                        ? String(errors.countryCode.message)
                        : undefined
                    }
                    options={COUNTRY_CODES}
                  />
                )}
              />
            </div>

            <div className="w-full">
              <Controller
                name="phoneNumber"
                control={control}
                render={({ field }) => (
                  <InputField
                    label={true}
                    field={field}
                    placeholder="Enter your mobile number"
                    errorMessage={errors.phoneNumber?.message}
                  />
                )}
              />
            </div>
          </div>
        </div>
        <Controller
          name="country"
          control={control}
          render={({ field }) => (
            <InputSelect
              label={'Country'}
              field={field}
              placeholder="Select country"
              errorMessage={
                errors.country?.message
                  ? String(errors.country.message)
                  : undefined
              }
              options={[{ value: '+61', label: 'Australia' }]}
              disabled
            />
          )}
        />
        <Controller
          name="state"
          control={control}
          render={({ field }) => (
            <InputSelect
              label={'State'}
              field={field}
              placeholder="Select your state"
              errorMessage={
                errors.state?.message ? String(errors.state.message) : undefined
              }
              options={
                stateListData?.data?.data?.map((state: StateData) => ({
                  label: state.name,
                  value: state.id.toString(),
                  license_formats: state.license_formats,
                })) || []
              }
              isLoading={stateListLoading}
            />
          )}
        />

        {selectedState &&
          selectedState.license_formats &&
          selectedState.license_formats.length > 0 && (
            <div className="relative mt-6">
              <div
                className={`flex ${hasPrefixes || hasPostfixes ? 'gap-x-2' : ''}`}
              >
                <label
                  className={clsx(
                    'text-sm font-medium text-left absolute -top-5 text-gray-700'
                  )}
                >
                  License Number
                </label>

                {hasPrefixes && (
                  <div className="w-[142px]">
                    <Controller
                      name="licensePrefix"
                      control={control}
                      render={({ field }) => (
                        <InputSelect
                          label={true}
                          field={field}
                          placeholder="Prefix"
                          errorMessage={
                            errors.licensePrefix?.message
                              ? String(errors.licensePrefix.message)
                              : undefined
                          }
                          options={getLicensePrefixOptions()}
                        />
                      )}
                    />
                  </div>
                )}

                <div
                  className={hasPrefixes || hasPostfixes ? 'w-full' : 'w-full'}
                >
                  <Controller
                    name="licenseNo"
                    control={control}
                    render={({ field: originalField }) => {
                      // Create a new field object with onFocus added
                      const field: ExtendedControllerRenderProps = {
                        ...originalField,
                        onChange: (e) =>
                          handleLicenseNumberChange(e, originalField.onChange),
                      };

                      return (
                        <InputField
                          label={true}
                          field={field}
                          onFocus={() => handleLicenseNumberFocus()} // Move onFocus to InputField props
                          placeholder="Enter your license number"
                          errorMessage={
                            licenseNoTouched
                              ? licenseError || errors.licenseNo?.message
                              : undefined
                          }
                        />
                      );
                    }}
                  />
                </div>

                {hasPostfixes && (
                  <div className="w-[142px]">
                    <Controller
                      name="licensePostfix"
                      control={control}
                      render={({ field }) => (
                        <InputSelect
                          label={true}
                          field={field}
                          placeholder="Postfix"
                          errorMessage={
                            errors.licensePostfix?.message
                              ? String(errors.licensePostfix.message)
                              : undefined
                          }
                          options={getLicensePostfixOptions()}
                        />
                      )}
                    />
                  </div>
                )}
              </div>
            </div>
          )}
        <span className="text-sm text-[#97959E] ">
          By signing up, you agree to our{' '}
          <Link
            to={PATHS.TERMS_CONDITIONS_PUBLIC}
            target="_blank"
            className="font-medium text-primary-100 underline underline-offset-2"
          >
            Terms and Conditions
          </Link>{' '}
          and{' '}
          <Link
            to={PATHS.PRIVACY_POLICY_PUBLIC}
            target="_blank"
            className="font-medium text-primary-100 underline underline-offset-2"
          >
            Privacy Policy
          </Link>
          .
        </span>

        <Button
          text="Sign Up"
          className="mt-2"
          type={isTNCAccepted ? 'submit' : 'button'}
          onClick={isTNCAccepted ? undefined : buttonClickHandler}
          loading={loading}
        />

        <Button
          text={
            <span className="font-normal">
              Already have an account? <span className="font-bold">Login</span>
            </span>
          }
          className="mt-2"
          variant="outline"
          type="button"
          onClick={() => navigate('/login')}
        />
        <p className="mt-2 font-normal text-center">
          Want to register as{' '}
          <span
            className="font-medium text-primary-100 cursor-pointer hover:underline"
            onClick={() => setIsModalOpen(true)}
          >
            Enterprise?
          </span>
        </p>
      </form>
    </div>
  );
};

export default Register;
