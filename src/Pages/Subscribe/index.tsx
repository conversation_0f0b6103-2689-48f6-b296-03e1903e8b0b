import Logo from '@Assets/Images/RexLogo.png';
import { <PERSON><PERSON>, Modal } from '@Components/UI';
import { CircleTickIcon, Loader, LogoutIcon } from '@Icons';
import { motion } from 'framer-motion';
import useScreenScale from '@Hooks/useScreenScale';
import clsx from 'clsx';
import { useEffect, useState } from 'react';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useLocation, useNavigate } from 'react-router';
import { useLogout } from '@Query/Hooks/useAuth';
import { useDispatch } from 'react-redux';
import queryClient from '@Helpers/QueryClient';
import { clearUserData } from '@Redux/SystemControl/UserControle';
import { PATHS } from '@Config/Path.Config';

interface Feature {
  id: number;
  name: string;
  display_name: string;
}

interface Plan {
  id: number;
  subscription_name: string;
  subscription_price: number;
  subscription_duration: number;
  no_of_uploads: number;
  features: Feature[];
  price_id: string;
  product_id: string;
  additional_report_price: number;
  additional_user_price: number;
}

const Subscribe = (): JSX.Element => {
  const scale = useScreenScale();
  const { addToast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const api = new Api();

  const currentYear = new Date().getFullYear();

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [isCreatingSession, setIsCreatingSession] = useState<string | null>(
    null
  );
  const [openLogoutModal, setOpenLogoutModal] = useState<boolean>(false);
  const {
    mutate: logout,
    isLoading: isLogoutLoading,
    isError,
    error,
    isSuccess,
    data,
  } = useLogout();

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }
  }, [isError, error]);

  useEffect(() => {
    if (isSuccess) {
      addToast('success', data?.data?.message);
      setOpenLogoutModal(false);
      localStorage.clear();
      sessionStorage.clear();
      dispatch(clearUserData());
      queryClient.clear();
      navigate(PATHS.LOGIN, { replace: true });
    }
  }, [isSuccess]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const paymentStatus = searchParams.get('payment_status');

    if (paymentStatus === 'failed') {
      addToast('error', 'Payment failed!');
      navigate('.', { replace: true }); // Clean URL
    }
  }, [location, navigate, addToast]);

  useEffect(() => {
    (async () => {
      try {
        setIsLoading(true);
        const response = await api.get(API_PATHS.GET_SUBSCRIPTION_PLAN);
        if (response?.data?.data?.length) {
          setPlans([...(response?.data?.data as Plan[])]);
        }
        if (!response.data?.status) addToast('error', response?.data?.message);
      } catch (err) {
        addToast('error', err as string);
      } finally {
        setIsLoading(false);
      }
    })();
  }, []);

  const getSessionForPlan = async (priceId: string) => {
    try {
      setIsCreatingSession(priceId);
      const sessionResponse = await api.post(API_PATHS.CREATE_STRIPE_SESSION, {
        data: {
          price_id: priceId,
          is_update: false,
        },
      });
      if (sessionResponse?.data?.data?.url && sessionResponse?.data?.status) {
        window.location.href = sessionResponse?.data?.data?.url;
      } else {
        addToast('error', sessionResponse?.data?.message as string);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsCreatingSession(null);
    }
  };

  return (
    <div className="flex flex-col h-screen w-screen pt-6 px-16 overflow-x-hidden relative">
      <div
        className="absolute top-10 right-6 cursor-pointer"
        onClick={() => setOpenLogoutModal(true)}
      >
        <div className="flex gap-x-2 items-center justify-center">
          <LogoutIcon height={18} width={18} fill={'#45E3C4'} />
          <h4
            className={clsx(
              'text-[#00D9B1] text-base font-semibold transition-all duration-300'
            )}
          >
            Logout
          </h4>
        </div>
      </div>
      <div className="flex h-[72px] w-[120px]">
        <img src={Logo} className="h-full w-full" />
      </div>
      <div className="flex flex-col items-center pt-8 pb-20 flex-1">
        <div
          className={clsx(
            'font-semibold text-2xl  text-center w-full lg:w-1/2',
            scale <= 100 ? 'lg:text-[46px]' : 'lg:text-[36px]'
          )}
        >
          Select the best plan for your needs
        </div>
        <div className="flex flex-1 h-full items-center justify-center">
          {isLoading ? (
            <div className="flex h-full w-full flex-1 justify-center items-center">
              <Loader height={32} width={32} fill="#fff" />
            </div>
          ) : (
            <div
              className={clsx(
                'grid grid-cols-2 lg:grid-cols-3 w-full px-0 pt-6',
                scale <= 100 ? 'gap-10 xl:px-44' : 'gap-6 xl:px-32 pt-8'
              )}
            >
              {plans.map((plan) => (
                <motion.div
                  key={plan?.id}
                  className="group border w-auto xl:min-w-[364px] border-b-primary rounded-lg p-8 bg:white shadow-primary flex flex-1 flex-col text-left text-black hover:text-white cursor-pointer"
                  whileHover={{
                    scale: 1.05,
                    boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)',
                    backgroundColor: 'var(--color-primary-100)',
                  }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                >
                  <div className="font-semibold text-3xl">
                    {plan?.subscription_name}
                  </div>
                  <div
                    className={clsx(
                      'font-semibold text-primary-100 group-hover:text-white',
                      scale <= 100 ? 'py-8 text-3xl' : 'py-4 text-2xl'
                    )}
                  >
                    <span>
                      <span
                        className={clsx(scale <= 100 ? 'text-5xl' : 'text-4xl')}
                      >
                        ${plan?.subscription_price}
                      </span>
                      /month
                    </span>
                  </div>
                  <div
                    key={plan?.no_of_uploads}
                    className={clsx(
                      'flex  items-center',
                      scale <= 100 ? 'pb-3.5' : 'pb-2'
                    )}
                  >
                    <div className="group-hover:fill-black pr-2 flex  h-full">
                      <div className="flex items-start justify-center flex-1 h-full pt-[5px] pb-1">
                        <CircleTickIcon
                          height={15}
                          width={15}
                          fill="currentColor"
                        />
                      </div>
                    </div>
                    <div className="font-bold h-full items-start justify-start">
                      {plan?.no_of_uploads} Reports per Month
                    </div>
                  </div>
                  <div
                    key={plan?.no_of_uploads}
                    className={clsx(
                      'flex items-center',
                      scale <= 100 ? 'pb-2' : 'pb-1 text-xs'
                    )}
                  >
                    <div className="group-hover:fill-black pr-2 flex  h-full">
                      <div className="flex items-start  justify-center h-full pt-[4px] pb-1">
                        <CircleTickIcon
                          height={15}
                          width={15}
                          fill="currentColor"
                        />
                      </div>
                    </div>
                    <div className="h-full items-start justify-start">
                      <span className="font-bold">
                        ${plan?.additional_report_price}.00
                      </span>{' '}
                      for each additional report
                    </div>
                  </div>
                  {plan?.id === 3 && (
                    <>
                      <div
                        key={plan?.no_of_uploads}
                        className={clsx(
                          'flex items-center',
                          scale <= 100 ? 'pb-2' : 'pb-1 text-xs'
                        )}
                      >
                        <div className="group-hover:fill-black pr-2 flex  h-full">
                          <div className="flex items-start  justify-center h-full pt-[4px] pb-1">
                            <CircleTickIcon
                              height={15}
                              width={15}
                              fill="currentColor"
                            />
                          </div>
                        </div>
                        <div className="h-full items-start justify-start">
                          Allows up to 4 users to access the app
                        </div>
                      </div>
                      <div
                        key={plan?.no_of_uploads}
                        className={clsx(
                          'flex items-center',
                          scale <= 100 ? 'pb-2' : 'pb-1 text-xs'
                        )}
                      >
                        <div className="group-hover:fill-black pr-2 flex  h-full">
                          <div className="flex items-start  justify-center h-full pt-[4px] pb-1">
                            <CircleTickIcon
                              height={15}
                              width={15}
                              fill="currentColor"
                            />
                          </div>
                        </div>
                        <div className="h-full items-start justify-start">
                          <span className="font-bold">
                            ${plan?.additional_user_price}.00
                          </span>{' '}
                          per month for every additional user
                        </div>
                      </div>
                    </>
                  )}
                  {plan?.features.map((benefit: Feature) => (
                    <div
                      key={benefit?.id}
                      className={clsx(
                        'flex items-center',
                        scale <= 100 ? 'pb-3.5' : 'pb-2'
                      )}
                    >
                      <div className="group-hover:fill-black pr-2 flex  h-full">
                        <div className="flex items-start justify-center flex-1 h-full pt-[5px] pb-1">
                          <CircleTickIcon
                            height={15}
                            width={15}
                            fill="currentColor"
                          />
                        </div>
                      </div>
                      <div className="font-normal h-full items-start justify-start">
                        Access to {benefit?.display_name}
                      </div>
                    </div>
                  ))}
                  <div className="flex flex-1 justify-center items-end">
                    <Button
                      text={'Subscribe now'}
                      className="mt-6 bg-primary-100 text-white group-hover:!bg-white group-hover:!text-primary-100"
                      variant="other"
                      loading={isCreatingSession === plan.price_id}
                      disabled={!!isCreatingSession}
                      onClick={() => {
                        getSessionForPlan(plan.price_id);
                        // navigate(PATHS.HOME);
                      }}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
      <div className="fixed w-full text-xs text-center bottom-0 py-2 bg-white text-secondary">
        Copyright © {currentYear} REX. All rights reserved.
      </div>

      <Modal
        children={
          <div className="flex flex-col items-center gap-[36px] justify-center">
            <div className="bg-[#CCF7EF] h-14 w-14 flex justify-center items-center rounded-full">
              <LogoutIcon width={18} height={18} fill="#00D9B1" />
            </div>
            <div>
              <h4 className="text-black text-2xl font-bold text-center">
                Are you sure want to logout this account?
              </h4>
            </div>
            <div className="flex w-full gap-6">
              <Button
                text="Yes"
                loading={isLogoutLoading}
                onClick={() => logout()}
              />
              <Button
                text="No"
                variant="outline"
                onClick={() => setOpenLogoutModal(false)}
              />
            </div>
          </div>
        }
        hideCloseButton
        isOpen={openLogoutModal}
        onClose={() => setOpenLogoutModal(false)}
      />
    </div>
  );
};

export default Subscribe;
