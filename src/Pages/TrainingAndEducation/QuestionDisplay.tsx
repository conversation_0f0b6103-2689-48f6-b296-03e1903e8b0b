import clsx from 'clsx';
import React from 'react';

interface Option {
  id: number;
  option_text: string;
}

interface Question {
  id: number;
  question: string;
  description?: string;
  question_type: 'MCQ' | 'MSQ';
  options: Option[];
}

interface Props {
  question: Question;
  selectedOptions: number[];
  onOptionSelect: (optionId: number) => void;
  currentQuestionIndex: number;
}

const QuestionDisplay: React.FC<Props> = ({
  question,
  selectedOptions,
  onOptionSelect,
  currentQuestionIndex,
}) => {
  const isMCQ = question.question_type === 'MCQ';

  return (
    <div>
      <h2 className="font-roboto font-bold text-lg leading-none tracking-normal align-middle mb-6 text-[#00D9B1]">
        Question-{currentQuestionIndex + 1}
      </h2>
      <p className="font-bold text-lg leading-7 tracking-normal align-middle mb-6">
        {question.question}
      </p>
      {question.description && (
        <p className="text-gray-600 mb-2">{question.description}</p>
      )}

      <div className="space-y-4">
        {question.options.map((option) => {
          const isSelected = selectedOptions.includes(option.id);

          if (isMCQ) {
            return (
              <div
                key={option.id}
                className={`flex items-center p-3 rounded border hover:cursor-pointer ${
                  isSelected
                    ? 'border-[#F1F1F1] bg-[#00D9B1]'
                    : 'border-[#F1F1F1] hover:bg-gray-50'
                }`}
                onClick={() => onOptionSelect(option.id)}
              >
                <div
                  className={`w-5 h-5 rounded-full flex items-center justify-center border ${
                    isSelected ? 'border-white' : 'border-gray-400'
                  }`}
                >
                  {isSelected && (
                    <div className="w-3 h-3 rounded-full bg-white"></div>
                  )}
                </div>
                <span
                  className={clsx(
                    'font-roboto text-lg ml-3',
                    isSelected ? 'text-white' : 'text-black'
                  )}
                >
                  {option.option_text}
                </span>
              </div>
            );
          } else {
            return (
              <div
                key={option.id}
                className={`flex items-center p-3 rounded border hover:cursor-pointer ${
                  isSelected
                    ? 'border-[#F1F1F1] bg-[#00D9B1]'
                    : 'border-[#F1F1F1] hover:bg-gray-50'
                }`}
                onClick={() => onOptionSelect(option.id)}
              >
                <div
                  className={`w-5 h-5 rounded flex items-center justify-center border ${
                    isSelected ? 'border-white' : 'border-gray-400'
                  }`}
                >
                  {isSelected && <div className="w-3 h-3 bg-white"></div>}
                </div>
                <span
                  className={clsx(
                    'font-roboto text-lg ml-3',
                    isSelected ? 'text-white' : 'text-black'
                  )}
                >
                  {option.option_text}
                </span>
              </div>
            );
          }
        })}
      </div>
    </div>
  );
};

export default QuestionDisplay;
