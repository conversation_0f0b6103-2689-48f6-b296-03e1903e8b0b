import clsx from 'clsx';
import React from 'react';

interface Question {
  id: number;
  question: string;
}

interface Props {
  questions: Question[];
  currentQuestionIndex: number;
  userAnswers: Record<number, number[]>;
  submittedQuestions: number[];
  onQuestionClick: (index: number) => void;
}

const QuestionsList: React.FC<Props> = ({
  questions,
  currentQuestionIndex,
  userAnswers,
  submittedQuestions,
  onQuestionClick,
}) => {
  return (
    <div className="bg-white rounded-lg shadow h-full overflow-y-auto custom-scrollbar">
      {/* <h2 className="text-xl font-bold mb-4">Questions</h2> */}
      <div className="">
        {questions.map((question, index) => {
          const isCurrentQuestion = index === currentQuestionIndex;
          const isSubmitted = submittedQuestions.includes(index);
          const isAnswered =
            isSubmitted &&
            userAnswers[question.id] &&
            userAnswers[question.id].length > 0;

          return (
            <div
              key={question.id}
              onClick={() => onQuestionClick(index)}
              className={`px-4 py-3 rounded cursor-pointer ${
                isCurrentQuestion
                  ? 'bg-[#D9FFF8]'
                  : isAnswered
                    ? 'bg-gray-100 hover:bg-gray-200'
                    : 'hover:bg-gray-100'
              }`}
            >
              <h3
                className={clsx(
                  'font-semibold text-[18px] align-middle mb-2',
                  isCurrentQuestion ? 'text-[#00D9B1]' : 'text-[#333333]'
                )}
              >
                Question-{index + 1}
              </h3>
              <p
                className={clsx(
                  'truncate font-normal text-[15px] align-middle',
                  isCurrentQuestion ? 'text-[#00D9B1]' : 'text-[#666666]'
                )}
              >
                {question.question}
              </p>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default QuestionsList;
