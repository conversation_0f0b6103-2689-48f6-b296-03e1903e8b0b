// src/components/ResultCard.tsx
import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router';

import PassedIcon from '@Assets/Images/CorrectIcon.png';
import FailedIcon from '@Assets/Images/AlertIcon.png';
import { BackIcon, Loader } from '@Icons';
import { useToast } from '@Components/UI/Toast/ToastProvider';

interface ResultCardState {
  score: number;
  totalScore: number;
  passed: boolean;
  title: string;
  description: string;
  certificate: string;
}

const ResultPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const navigate = useNavigate();
  const { addToast } = useToast();

  const location = useLocation();

  const { score, totalScore, title, description, passed, certificate } =
    (location.state as ResultCardState) || {
      score: 0,
      totalScore: 0,
      passed: false,
      description: '',
      title: '',
      certificate: '',
    };

  const handleDownload = async () => {
    if (passed && certificate) {
      setLoading(true);

      try {
        const res = await fetch(certificate, {
          method: 'GET',
          headers: { 'Content-Type': 'application/pdf' },
        });
        if (!res.ok) throw new Error(`Status ${res.status}`);
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'certificate.pdf';
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      } catch (err: any) {
        addToast('error', err as string);
      } finally {
        setLoading(false);
      }
    } else {
      navigate(-2);
    }
  };

  const handleBack = () => {
    navigate(-2);
  };

  return (
    <div className="flex flex-col w-full h-screen p-6">
      <div className="flex items-center mb-6">
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 mr-4 hover:cursor-pointer"
        >
          <BackIcon />
        </button>
        <h1 className="font-roboto font-semibold text-2xl">Result</h1>
      </div>
      <div
        className="w-[70%] h-[70%] m-auto bg-white rounded-xl p-8 flex flex-col items-center justify-center space-y-6"
        style={{ boxShadow: '0 0 10px rgba(0,0,0,0.1)' }}
      >
        {/* Logo */}
        <div className="mx-auto w-20 h-20">
          <img
            src={passed ? PassedIcon : FailedIcon}
            alt="Result logo"
            className="w-full h-full object-contain"
          />
        </div>

        <div className="text-4xl font-bold text-[#333333]">{`${score}/${totalScore}`}</div>

        <div className="text-lg font-semibold">{title}</div>

        <p className="font-roboto font-normal text-lg leading-[200%] tracking-normal text-center text-[#666666]">
          {description}
        </p>

        {passed && (
          <button
            onClick={handleDownload}
            disabled={loading}
            className={`
            w-full max-w-xs mx-auto py-2 px-4
            border-1 border-[#00D9B1]
            rounded-lg
            text-[#00D9B1] font-medium
            hover:bg-green-50
            disabled:opacity-50 disabled:cursor-not-allowed
            transition hover:cursor-pointer
          `}
          >
            {loading ? (
              <Loader height={20} width={20} fill="#00D9B1" />
            ) : (
              'Download Certificate'
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default ResultPage;
