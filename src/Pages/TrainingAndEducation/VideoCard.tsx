import { DurationIcon, VideoLockIcon, VideoUserIcon } from '@Icons';
import TempVideoPlaceholder from '@Assets/Images/TempVideoPlaceholder.jpg';

interface VideoData {
  id: number;
  title: string;
  thumbnail_url: string | null;
  duration: number | null;
  total_watched_duration: number;
  price: number;
  is_purchased: boolean;
  author_name: string | null;
  description: string | null;
  video_url: string;
  views: number;
  category: number;
  category_name: string;
  last_watched_at: string | null;
}

interface VideoCardProps {
  video: VideoData;
  onClick: () => void;
  isViewAll?: boolean;
}

export const VideoCard: React.FC<VideoCardProps> = ({
  video,
  onClick,
  isViewAll = false,
}) => {
  const progressPercentage =
    video.duration && video.duration > 0
      ? Math.min(100, (video.total_watched_duration / video.duration) * 100)
      : 0;

  const formatDuration = (ms: number | null): string => {
    if (!ms) return '0:00';

    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatPrice = (price: number): string => {
    return `$${price.toFixed(2)}`;
  };

  const thumbnailUrl = video.thumbnail_url || TempVideoPlaceholder;

  const authorName = video.author_name || 'Unknown';

  return (
    <div
      className="w-[calc(50%-12px)] md:w-[calc(50%-12px)] xl:w-[calc(33%-18px)] 2xl:w-[calc(25%-24px)] flex-shrink-0 rounded-lg overflow-hidden border border-gray-200 hover:bg-gray-50 transition-colors cursor-pointer"
      onClick={onClick}
    >
      <div className="relative">
        <div
          className={`relative aspect-video ${!video.is_purchased ? 'blur-[8px]' : ''}`}
        >
          <img
            src={thumbnailUrl}
            alt={video.title}
            className="w-full h-full object-cover"
          />

          {video.total_watched_duration > 0 && video.duration && (
            <div className="absolute bottom-0 left-0 w-full h-1 bg-gray-200">
              <div
                className="h-full bg-red-600"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          )}
        </div>

        {!video.is_purchased && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <VideoLockIcon />
          </div>
        )}
      </div>

      {/* Video Info */}
      <div className="p-4 flex gap-4 justify-between flex-col">
        <h3 className="font-roboto font-semibold text-base leading-tight align-middle md:text-[16px] lg:text-[18px] truncate">
          {video.title}
        </h3>
        <div>
          <div className="flex justify-start items-center mb-2">
            <div className="flex items-center">
              <div className="w-5 h-5 md:w-6 md:h-6 rounded-full overflow-hidden ml-[-2px] mr-1 md:mr-2">
                <VideoUserIcon />
              </div>
              <span className="font-roboto font-normal text-[14px] leading-[100%] align-middle md:text-sm text-[#333333]">
                {authorName}
              </span>
            </div>

            <div className="flex items-center ml-10">
              <DurationIcon />

              <span className="font-roboto font-normal text-[14px] leading-[100%] align-middle md:text-sm text-[#333333]">
                {formatDuration(video.duration)}
              </span>
            </div>
          </div>
          {isViewAll && (
            <span className="font-roboto font-normal text-[14px] leading-[100%] align-middle md:text-sm text-[#333333]">
              {video.category_name}
            </span>
          )}
          <div className="font-roboto font-bold text-base md:text-[16px] lg:text-[18px] align-middle text-[#4DE4C8] mt-2">
            {formatPrice(video.price)}
          </div>
        </div>
      </div>
    </div>
  );
};
