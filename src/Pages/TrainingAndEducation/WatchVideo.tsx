import React, { useEffect, useRef, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import ReactPlayer from 'react-player';
import { useGetVideoDetail, usePurchaseVideo } from '@Query/Hooks/useTraining';
import TempVideoPlaceholder from '@Assets/Images/TempVideoPlaceholder.jpg';
import {
  DurationIcon,
  VideoLockIcon,
  VideoUserIcon,
  PlayIcon,
  BackIcon,
  Loader,
  VideoFinishIcon,
} from '@Icons';
import Api from '@Helpers/Api';
import { Modal } from '@Components/UI';
import clsx from 'clsx';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { PATHS } from '@Config/Path.Config';

interface VideoDetailData {
  id: number;
  title: string;
  description: string;
  video_url: string;
  thumbnail_url: string | null;
  price: number;
  duration: number;
  views: number;
  category: number;
  category_name: string;
  is_purchased: boolean;
  is_active: boolean;
  author_name: string | null;
  last_watched_at: string | null;
  total_watched_duration: number;
  is_completed: boolean;
  quiz_pass: boolean;
  certificate: {
    certificate_url: string;
  } | null;
}

const VideoDetailPage: React.FC = () => {
  const api = new Api();
  const { id } = useParams<{
    id: string;
  }>();
  const { addToast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();

  const [loading, setLoading] = useState<boolean>(false);
  const [updateDurationLoading, setUpdateDurationLoading] =
    useState<boolean>(false);
  const [videoData, setVideoData] = useState<VideoDetailData | null>(null);
  const [showCompletionModal, setShowCompletionModal] =
    useState<boolean>(false);
  const [isBackFromStripe, setIsBackFromStripe] = useState<boolean>(false);
  const [purchaseItemIsLoading, setPurchaseItemLoading] =
    useState<boolean>(false);
  const playerRef = useRef<ReactPlayer>(null);
  const playedSecondsRef = useRef<number>(0);
  const [shouldStartPlaying, setShouldStartPlaying] = useState<boolean>(false);
  const [initialStartPosition, setInitialStartPosition] = useState<
    number | null
  >(null);

  const { isLoading, isFetching } = useGetVideoDetail(Number(id), {
    onSuccess: (response) => {
      setVideoData(response.data.data);
      const totalDuration = response.data.data.duration || 1;
      const watchedDuration = response.data.data.total_watched_duration || 0;

      if (watchedDuration > 0 && response.data.data.is_purchased) {
        const startPosition = watchedDuration / totalDuration;
        const validStartPosition = Math.min(Math.max(0, startPosition), 0.99);
        setInitialStartPosition(validStartPosition);
      }
    },
    onError: (error) => {
      addToast('error', error as string);
    },
  });

  const { mutate: purchaseItem } = usePurchaseVideo({
    onSuccess: (data) => {
      if (data?.data?.data?.checkout_url) {
        window.location.replace(data?.data?.data?.checkout_url);
      } else {
        setPurchaseItemLoading(false);
      }
    },
    onError: (error) => {
      setPurchaseItemLoading(false);
      addToast('error', error as string);
    },
  });

  const handleEnded = () => {
    if (videoData?.is_purchased) {
      setVideoData((prev) => (prev ? { ...prev, is_completed: true } : null));
      // updateWatchProgress(true);
      if (!videoData?.quiz_pass) {
        setShowCompletionModal(true);
      }
    }
  };

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const paymentStatus = searchParams.get('payment_status');

    if (paymentStatus === 'success' || paymentStatus === 'failed') {
      setIsBackFromStripe(true);
    }
  }, [location, navigate]);

  const handleReady = () => {
    if (initialStartPosition !== null && playerRef.current) {
      playerRef.current.seekTo(initialStartPosition, 'fraction');
      setInitialStartPosition(null);
      setShouldStartPlaying(true);
    }
  };

  useEffect(() => {
    return () => {
      if (videoData?.is_purchased && playedSecondsRef.current > 0) {
        updateWatchProgress(false, playedSecondsRef.current);
      }
    };
  }, [videoData]);

  const handleProgress = (state: { playedSeconds: number; played: number }) => {
    const currentPlayedMs = state.playedSeconds * 1000;

    playedSecondsRef.current = currentPlayedMs;
  };

  const updateWatchProgress = async (
    completed = false,
    duration = playedSecondsRef.current
  ) => {
    try {
      setUpdateDurationLoading(true);
      await api.post('training-education/update-watched-duration/', {
        data: {
          video_id: videoData?.id,
          duration: completed ? videoData?.duration : duration,
        },
      });
    } catch (error) {
      addToast('error', error as string);
    } finally {
      setUpdateDurationLoading(false);
    }
  };

  const formatDuration = (ms: number | null): string => {
    if (!ms) return '0:00';

    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // const handleTakeQuiz = () => {
  //   setShowCompletionModal(false);
  //   navigate(`/quiz/${videoData?.id}`);
  // };

  const handleCloseModal = () => {
    setShowCompletionModal(false);
  };

  const formatPrice = (price: number): string => {
    return `$${price?.toFixed(2)}`;
  };

  const handleBack = () => {
    if (videoData?.is_purchased && playedSecondsRef.current > 0) {
      updateWatchProgress(false, playedSecondsRef.current);
    }
    if (isBackFromStripe) {
      navigate(PATHS.TRAINING);
    } else {
      navigate(-1);
    }
  };

  const handleActionButtonClick = async () => {
    if (!videoData) return;

    if (!videoData.is_purchased) {
      setPurchaseItemLoading(true);
      purchaseItem({ video_id: videoData.id });
    } else if (videoData?.certificate) {
      try {
        setLoading(true);
        const res = await fetch(videoData?.certificate?.certificate_url, {
          method: 'GET',
          headers: { 'Content-Type': 'application/pdf' },
        });
        if (!res.ok) throw new Error(`Status ${res.status}`);
        const blob = await res.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'certificate.pdf';
        document.body.appendChild(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
      } catch (err: any) {
        addToast('error', err as string);
      } finally {
        setLoading(false);
      }
    } else {
      navigate(`/quiz/${videoData.id}`, { state: { videoId: videoData.id } });
    }
  };

  const thumbnailUrl = videoData?.thumbnail_url || TempVideoPlaceholder;

  return isLoading || isFetching ? (
    <div className="w-full h-full flex flex-col items-center justify-center">
      <Loader height={40} width={40} fill="#fff" />
      {/* <br />
      Loading... */}
    </div>
  ) : (
    <>
      <div className="flex flex-col w-full p-6">
        <div className="flex items-center mb-6">
          <button
            onClick={handleBack}
            className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 mr-4 hover:cursor-pointer"
          >
            <BackIcon />
          </button>
          <h1 className="font-roboto font-semibold text-2xl">
            {videoData?.category_name}
          </h1>
        </div>

        <div
          className="w-full h-[65dvh] rounded-lg overflow-hidden bg-black mb-6 relative"
          onContextMenu={(e) => e.preventDefault()}
        >
          {videoData?.is_purchased ? (
            <ReactPlayer
              ref={playerRef}
              url={videoData?.video_url}
              width="100%"
              height="100%"
              controls
              playing={shouldStartPlaying}
              light={thumbnailUrl}
              playIcon={<PlayIcon height={'100px'} width={'100px'} />}
              config={{
                file: {
                  attributes: {
                    controlsList: 'nodownload',
                    disablePictureInPicture: true,
                    nContextMenu: (e: React.MouseEvent) => e.preventDefault(),
                  },
                },
              }}
              onEnded={handleEnded}
              onProgress={handleProgress}
              onReady={handleReady}
              progressInterval={1000}
            />
          ) : (
            <div className="relative w-full h-full">
              <img
                src={thumbnailUrl}
                alt={videoData?.title}
                className="w-full h-full object-cover blur"
              />
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <VideoLockIcon height={'100px'} width={'100px'} />
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col w-full">
          <div className="flex justify-between items-start">
            <div className="flex flex-col gap-2">
              <span className="font-semibold text-[32px] leading-[100%] tracking-[0px] align-middle font-roboto">
                {videoData?.title}
              </span>
              <div className="flex items-center mb-4">
                <div className="flex items-center mr-6">
                  <div className="w-6 h-6 rounded-full overflow-hidden ml-[-2px] mr-2">
                    <VideoUserIcon />
                  </div>
                  <span className="font-roboto font-normal text-[14px] leading-[100%] tracking-[0px] align-middle text-[#333333]">
                    {videoData?.author_name || 'Unknown'}
                  </span>
                </div>

                <div className="flex items-center mr-6">
                  <DurationIcon />
                  <span className="font-roboto font-normal text-[14px] leading-[100%] tracking-[0px] align-middle text-[#333333]">
                    {formatDuration(videoData?.duration ?? 0)}
                  </span>
                </div>
              </div>
            </div>
            {(videoData?.is_completed || !videoData?.is_purchased) &&
              (!videoData?.quiz_pass ||
                videoData?.certificate ||
                !videoData?.is_purchased) && (
                <button
                  onClick={handleActionButtonClick}
                  disabled={
                    purchaseItemIsLoading ||
                    loading ||
                    (videoData?.is_purchased && !videoData?.is_completed)
                  }
                  className={clsx(
                    'h-[54px] pt-3 pr-[35px] pb-3 pl-[35px] gap-[6px] rounded-[10px] bg-[#4DE4C8] font-roboto font-semibold text-[18px] text-white',
                    videoData?.is_purchased ? 'w-[230px]' : 'w-[200px]',
                    purchaseItemIsLoading ||
                      (videoData?.is_purchased && !videoData?.is_completed)
                      ? 'hover:cursor-not-allowed'
                      : 'hover:cursor-pointer'
                  )}
                >
                  {purchaseItemIsLoading || loading ? (
                    <Loader height={20} width={20} fill="#fff" />
                  ) : videoData?.is_purchased ? (
                    videoData?.certificate ? (
                      'Certificate'
                    ) : (
                      'Fill Questionnaire'
                    )
                  ) : (
                    'Purchase Now'
                  )}
                </button>
              )}
          </div>

          <div className="font-roboto font-bold text-[18px] leading-[100%] tracking-[0px] align-middle text-[#00D9B1] mb-2">
            {formatPrice(videoData?.price ?? 0)}
          </div>
          <div>
            <span className="font-semibold text-[16px] leading-[100%] tracking-[0px] align-middle font-roboto">
              Description
            </span>
            <p className="font-roboto font-normal text-[14px] leading-5 text-[#333333] mt-2">
              {videoData?.description}
            </p>
          </div>
        </div>
      </div>
      {showCompletionModal && (
        <Modal
          isOpen={showCompletionModal}
          onClose={() => {
            handleCloseModal();
          }}
          header={false}
          size="sm"
        >
          <div className="w-full max-w-md mx-auto bg-white rounded-lg p-5 flex flex-col items-center justify-center">
            <div className="mb-10">
              <div className="w-16 h-16 rounded-full border border-gray-300 flex items-center justify-center">
                <VideoFinishIcon />
              </div>
            </div>

            <h2 className="font-bold text-[30px] tracking-[0px] text-center align-middle mb-0 font-roboto">
              Thank You
            </h2>

            <p className="font-bold text-[18px] leading-[100%] tracking-[0px] text-center align-middle font-roboto mb-8 text-[#666666]">
              for watching this video
            </p>

            <button
              className="w-[260px] h-[60px] px-[35px] py-[12px] gap-[6px] rounded-[10px] bg-[#4DE4C8] font-semibold text-[18px] font-roboto text-[#fff] hover:cursor-pointer"
              onClick={() => handleActionButtonClick()}
              disabled={updateDurationLoading}
            >
              {updateDurationLoading ? (
                <Loader height={20} width={20} fill="#fff" />
              ) : (
                'Fill the Questionary'
              )}
            </button>
          </div>
        </Modal>
      )}
    </>
  );
};

export default VideoDetailPage;
