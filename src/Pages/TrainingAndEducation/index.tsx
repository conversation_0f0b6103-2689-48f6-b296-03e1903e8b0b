import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router';
import { useGetVideoListings } from '@Query/Hooks/useTraining';
import { Loader } from '@Icons';
import { VideoCard } from './VideoCard';
import { useToast } from '@Components/UI/Toast/ToastProvider';

interface VideoData {
  id: number;
  title: string;
  thumbnail_url: string | null;
  duration: number | null;
  total_watched_duration: number;
  price: number;
  is_purchased: boolean;
  author_name: string | null;
  description: string | null;
  video_url: string;
  views: number;
  category: number;
  category_name: string;
  last_watched_at: string | null;
}

interface CategoryData {
  id: number;
  category_name: string;
  description: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  video_count: number;
  videos: VideoData[];
}

const VideoListingPage: React.FC = () => {
  const navigate = useNavigate();
  const [categories, setCategories] = useState<
    {
      category: CategoryData;
      videos: VideoData[];
    }[]
  >([]);
  const { addToast } = useToast();
  const scrollRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});
  const [scrollStates, setScrollStates] = useState<{
    [key: number]: { showLeft: boolean; showRight: boolean };
  }>({});

  const { isFetching, isLoading } = useGetVideoListings({
    onSuccess: (response) => {
      const list = response.data.data.list;
      const recentIdx = list.findIndex((item: any) => item.category.id === 1);
      let reordered = [...list];
      if (recentIdx > -1) {
        const [recentItem] = reordered.splice(recentIdx, 1);
        reordered = [recentItem, ...reordered];
      }
      setCategories(reordered);
    },
    onError: (error) => {
      addToast('error', error as string);
    },
  });

  const checkScroll = useCallback(() => {
    categories.forEach(({ category }) => {
      const el = scrollRefs.current[category.id];
      if (el) {
        const { scrollLeft, scrollWidth, clientWidth } = el;
        setScrollStates((prev) => ({
          ...prev,
          [category.id]: {
            showLeft: scrollLeft > 0,
            showRight: scrollLeft < scrollWidth - clientWidth - 10,
          },
        }));
      }
    });
  }, [categories]);

  //  don't remove until the new flow has been decide for the training page category
  useEffect(() => {
    navigate('/category/Training Video/3', {});
  }, []);
  useEffect(() => {
    checkScroll();
    categories.forEach(({ category }) => {
      const el = scrollRefs.current[category.id];
      if (el) el.addEventListener('scroll', checkScroll);
    });
    return () => {
      categories.forEach(({ category }) => {
        const el = scrollRefs.current[category.id];
        if (el) el.removeEventListener('scroll', checkScroll);
      });
    };
  }, [categories, checkScroll]);

  useEffect(() => {
    window.addEventListener('resize', checkScroll);
    return () => window.removeEventListener('resize', checkScroll);
  }, [checkScroll]);

  const scrollBy = (id: number, direction: 'left' | 'right') => {
    const el = scrollRefs.current[id];
    if (el) {
      const amount = el.clientWidth * 0.8;
      el.scrollBy({
        left: direction === 'left' ? -amount : amount,
        behavior: 'smooth',
      });
    }
  };

  const handleVideoClick = (id: number) => navigate(`/video/${id}`);
  const handleViewAll = (id: number, name: string) => {
    console.log('id', id);
    console.log('name', name);
    navigate(`/category/${name}/${id}`, {});
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-10 pb-6">
        <h1 className="font-roboto font-semibold text-[32px]">
          Training & Education
        </h1>
      </div>
      {isLoading || isFetching ? (
        <div className="w-full h-full flex flex-col items-center justify-center">
          <Loader height={40} width={40} fill="#fff" />
          {/* <br />
          Loading... */}
        </div>
      ) : !categories.length ? (
        <div className="w-full h-full flex items-center justify-center">
          No Videos Found
        </div>
      ) : (
        <>
          <div className="pt-0 h-dvh custom-scrollbar p-10">
            {categories
              .filter(({ category }) => category.id !== 1 && category.id !== 2)
              .map(({ category, videos }) => (
                <div key={category.id} className="mb-6">
                  <div className="flex justify-between items-center mb-4">
                    <span className="font-semibold text-[24px] font-roboto">
                      {category.id === 1
                        ? 'Recent Viewed'
                        : category.category_name}
                    </span>
                    {videos?.length >= 5 && (
                      <button
                        onClick={() =>
                          handleViewAll(category.id, category.category_name)
                        }
                        className="text-[18px] font-roboto text-[#00D9B1] hover:cursor-pointer"
                      >
                        See All
                      </button>
                    )}
                  </div>

                  <div className="relative w-full">
                    {scrollStates[category.id]?.showLeft && (
                      <button
                        onClick={() => scrollBy(category.id, 'left')}
                        className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white bg-opacity-70 hover:bg-opacity-100 rounded-full p-2 shadow-md border border-gray-200 hover:cursor-pointer"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 19l-7-7 7-7"
                          />
                        </svg>
                      </button>
                    )}

                    {scrollStates[category.id]?.showRight && (
                      <button
                        onClick={() => scrollBy(category.id, 'right')}
                        className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white bg-opacity-70 hover:bg-opacity-100 rounded-full p-2 shadow-md border border-gray-200 hover:cursor-pointer"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-6 w-6 text-gray-700"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </button>
                    )}

                    <div
                      ref={(el) => (scrollRefs.current[category.id] = el)}
                      className="w-full overflow-x-auto scrollbar-hide"
                    >
                      <div className="flex space-x-6">
                        {videos.length > 0 ? (
                          videos.map((video) => (
                            <VideoCard
                              key={video.id}
                              video={video}
                              onClick={() => handleVideoClick(video.id)}
                            />
                          ))
                        ) : (
                          <p className="text-gray-500">No videos available</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </>
      )}
    </div>
  );
};

export default VideoListingPage;
