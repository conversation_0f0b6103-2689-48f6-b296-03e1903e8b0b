import CacheKeys from '@Helpers/CacheKeys';
import { useQuery, useMutation } from 'react-query';

import {
  viewUserProfile,
  userLicenseUpdate,
} from '@Query/Services/admin.service';

export const useGetUserProfile = () => {
  return useQuery(CacheKeys.profileData, viewUserProfile, {
    staleTime: 1000 * 60 * 5,
  });
};

export const useUserLicenseUpdate = () =>
  useMutation((data: Record<string, any>) => {
    const { license, format_id } = data; // Extract only required fields
    return userLicenseUpdate({
      license,
      format_id,
    });
  });
