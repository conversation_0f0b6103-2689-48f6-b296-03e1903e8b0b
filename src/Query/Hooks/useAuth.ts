import CacheKeys from '@Helpers/CacheKeys';
import {
  userLogin,
  userVerifyOtp,
  userResendOtp,
  userRegister,
  getCountryList,
  getStateList,
  userLogout,
  userDeleteAccount,
} from '@Query/Services/auth.service';
import { useMutation, useQuery } from 'react-query';

export const useLogout = () => useMutation(userLogout);
export const useDeleteAccount = () => useMutation(userDeleteAccount);

export const useLogin = () =>
  useMutation((data: Record<string, any>) => {
    const { mobile_no, country_code, device_id, device_token } = data;
    return userLogin({ mobile_no, country_code, device_id, device_token });
  });

export const useVerifyOtp = () =>
  useMutation((data: Record<string, any>) => {
    const { verify_token, otp } = data;
    return userVerifyOtp({ verify_token, otp });
  });

export const useResendOtp = () =>
  useMutation((data: Record<string, any>) => {
    const { verify_token, user_id } = data;
    return userResendOtp({ verify_token, user_id });
  });

export const useRegister = () =>
  useMutation((data: Record<string, any>) => {
    const {
      first_name,
      last_name,
      email,
      mobile,
      country_code,
      country,
      state,
      device_id,
      license,
      device_token,
      format_id,
    } = data; // Extract only required fields
    return userRegister({
      first_name,
      last_name,
      email,
      mobile,
      country_code,
      country,
      state,
      device_id,
      license,
      device_token,
      format_id,
    });
  });

export const useCountryList = () =>
  useQuery(CacheKeys.countryList, getCountryList);

export const useStateList = () => useQuery(CacheKeys.stateList, getStateList);
