import CacheKeys from '@Helpers/CacheKeys';
import {
  addEnterpriseMember,
  bulkUploadMembersByCsv,
  deleteEnterprise,
  deleteEnterpriseMember,
  downloadSampleCsvOfBulkUpload,
  editEnterprise,
  editEnterpriseMember,
  getEnterpriseList,
  getEnterpriseRequest,
  getEnterpriseWiseMembers,
  makeEnterpriseRequestApprove,
  makeEnterpriseRequestReject,
  toggleEnterpriseMember,
  viewUserBusinessData,
  updateBusinessData,
  deleteSubUser,
} from '@Query/Services/enterprise.service';
import { useMutation, UseMutationOptions, useQuery } from 'react-query';

type UpdateBusinessPayload = {
  formData: FormData;
  orgId: string;
  isOrgAdmin?: boolean;
};

type MemberInput = {
  orgId: number;
  first_name: string;
  last_name: string;
  mobile_no: string;
  country_code: string;
  email: string;
  userId?: number;
};

type AddUserInput = Omit<MemberInput, 'orgId'>;

export const useUpdateBusinessData = (
  options?: UseMutationOptions<any, any, UpdateBusinessPayload>
) => {
  return useMutation(
    ({ formData, orgId, isOrgAdmin }: UpdateBusinessPayload) => {
      return updateBusinessData(formData, orgId, isOrgAdmin as boolean);
    },
    {
      ...options,
    }
  );
};

interface UseGetUserBusinessDataProps {
  orgId: number;
  enabled?: boolean;
  isOrgAdmin?: boolean;
}

export const useGetUserBusinessData = (props: UseGetUserBusinessDataProps) => {
  const { orgId, enabled = true, isOrgAdmin } = props;

  return useQuery(
    [CacheKeys.businessData, orgId],
    () => viewUserBusinessData({ orgId, isOrgAdmin: isOrgAdmin as boolean }),
    { enabled }
  );
};

export const useGetEnterpriseRequests = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
}) =>
  useQuery(CacheKeys.enterpriseReqList, () =>
    getEnterpriseRequest({ ...props })
  );

export const useApproveEnterpriseRequest = () =>
  useMutation(
    (data: {
      id: number;
      first_name: string;
      last_name: string;
      email: string;
      mobile: string;
      country_code: number | any;
      license: string;
      state: number;
      country: number;
      company_name: string;
      organization_size: number;
      format_id: number;
      allowed_number_of_uploads: number;
      price: number;
      subscription_start_date: string;
      subscription_end_date: string;
      feature_ids: number[];
      logo: File;
    }) => makeEnterpriseRequestApprove({ ...data })
  );

export const useRejectEnterpriseRequest = () =>
  useMutation((data: { id: number }) =>
    makeEnterpriseRequestReject({ ...data })
  );

export const useGetEnterpriseList = (props: {
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
}) => useQuery(CacheKeys.enterpriseList, () => getEnterpriseList({ ...props }));

export const useGetEnterpriseMember = (props: {
  orgId: number;
  ordering: string;
  page: number;
  limit: number;
  isSearchable?: boolean;
  field?: string;
  search?: string;
  enabled?: boolean;
}) =>
  useQuery(
    CacheKeys.enterpriseMembersList,
    () => getEnterpriseWiseMembers({ ...props }),
    { enabled: props.enabled }
  );

export const useDeleteEnterprise = () =>
  useMutation((data: { orgId: number }) => deleteEnterprise({ ...data }));

export const useEditEnterprise = () =>
  useMutation(
    (data: {
      orgId: number;
      first_name: string;
      last_name: string;
      email: string;
      mobile: string;
      country_code: number;
      license: string;
      state: number;
      country: number;
      company_name: string;
      organization_size: number;
      format_id: number;
      allowed_number_of_uploads?: number;
      price?: number;
      subscription_start_date?: string;
      subscription_end_date?: string;
      feature_ids?: number[];
      logo?: File;
    }) => editEnterprise({ ...data })
  );

export const useToggleMember = () =>
  useMutation((data: { orgId: number; memberId: number }) =>
    toggleEnterpriseMember({ ...data })
  );

export const useAddEnterpriseMember = (type: 'member' | 'sub-user') => {
  return useMutation((data: AddUserInput | MemberInput) => {
    return addEnterpriseMember({ ...data }, type);
  });
};

export const useEditEnterpriseMember = (type: 'member' | 'sub-user') =>
  useMutation((data: AddUserInput | MemberInput) =>
    editEnterpriseMember({ ...data }, type)
  );

export const useDeleteEnterpriseMember = () =>
  useMutation((data: { orgId: number; listId: number }) =>
    deleteEnterpriseMember({ ...data })
  );

export const useDeleteSubUser = () =>
  useMutation((data: { userId: number }) => deleteSubUser({ ...data }));

export const useSampleEnterpriseCsv = () =>
  useMutation(() => downloadSampleCsvOfBulkUpload());

export const useImportEnterpriseCsv = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  options?: UseMutationOptions<any, any, FormData>,
  orgId?: number
) => {
  return useMutation(
    (formData: FormData) => bulkUploadMembersByCsv(formData, orgId ?? 0),
    {
      ...options,
    }
  );
};
