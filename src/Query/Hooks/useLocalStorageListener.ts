import { useEffect, useState } from 'react';

export const useLocalStorageListener = (key: string) => {
  const [value, setValue] = useState(() => localStorage.getItem(key));

  useEffect(() => {
    const handler = (e: StorageEvent) => {
      if (e.key === key) {
        setValue(e.newValue);
      }
    };

    window.addEventListener('storage', handler);
    return () => window.removeEventListener('storage', handler);
  }, [key]);

  return value;
};
