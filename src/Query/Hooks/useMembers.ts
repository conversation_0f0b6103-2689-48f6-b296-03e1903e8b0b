import CacheKeys from '@Helpers/CacheKeys';
import {
  getSubscriptionWiseMembers,
  purchaseMembers,
} from '@Query/Services/subscription.service';
import { useMutation, UseMutationOptions, useQuery } from 'react-query';

export const useGetSubscriptionMember = (data?: any) =>
  useQuery(CacheKeys.subscriptionMemberList, () =>
    getSubscriptionWiseMembers(data)
  );

export const usePurchaseMembers = (
  options?: UseMutationOptions<any, any, any>
) => {
  return useMutation((data: any) => purchaseMembers(data), { ...options });
};
