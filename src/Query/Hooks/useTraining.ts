import {
  useMutation,
  UseMutationOptions,
  useQuery,
  UseQueryOptions,
} from 'react-query';
import { AxiosResponse } from 'axios';

import CacheKeys from '@Helpers/CacheKeys';
import {
  fetchCategories,
  fetchCategoryVideos,
  fetchVideoListings,
  getAnswers,
  getQuizDetails,
  getVideoDetails,
  purchaseUploadReportVideo,
  purchaseVideo,
  submitQuiz,
} from '@Query/Services/training.service';

export const useGetVideoListings = (
  options?: UseQueryOptions<AxiosResponse, unknown>
) => {
  return useQuery<AxiosResponse, unknown>(
    CacheKeys.videoList,
    fetchVideoListings,
    {
      refetchOnMount: 'always',
      ...options,
    }
  );
};
export const useGetVideoDetail = (
  videoId: number,
  options?: UseQueryOptions<AxiosResponse, unknown>
) => {
  return useQuery<AxiosResponse, unknown>(
    CacheKeys.videoDetail,
    () => getVideoDetails(videoId),
    {
      refetchOnMount: 'always',
      ...options,
    }
  );
};

export const useGetQuizDetail = (
  videoId: number,
  options?: UseQueryOptions<AxiosResponse, unknown>
) => {
  return useQuery<AxiosResponse, unknown>(
    CacheKeys.quizDetail,
    () => getQuizDetails(videoId),
    {
      refetchOnMount: 'always',
      ...options,
    }
  );
};

export const useGetQuizAnswers = (
  quizId: number,
  options?: UseQueryOptions<AxiosResponse, unknown>
) => {
  return useQuery<AxiosResponse, unknown>(
    CacheKeys.answerDetail,
    () => getAnswers(quizId),
    {
      refetchOnMount: 'always',
      ...options,
    }
  );
};

export const useSubmitQuiz = (options?: UseMutationOptions<any, any, any>) => {
  return useMutation((data: any) => submitQuiz(data), {
    ...options,
  });
};

export const usePurchaseVideo = (
  options?: UseMutationOptions<any, any, any>
) => {
  return useMutation((data: any) => purchaseVideo(data), {
    ...options,
  });
};

export const usePurchaseReportUpload = (
  options?: UseMutationOptions<any, any, any>
) => {
  return useMutation((data: any) => purchaseUploadReportVideo(data), {
    ...options,
  });
};

export const useGetCategoryVideos = (
  categoryId: string | null,
  page: number,
  options?: UseQueryOptions<AxiosResponse, unknown>
) => {
  return useQuery<AxiosResponse, unknown>(
    [CacheKeys.categoryVideoList, categoryId, page],
    () => fetchCategoryVideos(categoryId, page),
    {
      refetchOnMount: 'always',
      ...options,
    }
  );
};
export const useGetCategories = (
  options?: UseQueryOptions<AxiosResponse, unknown>
) => {
  return useQuery<AxiosResponse, unknown>(
    CacheKeys.categoryList,
    fetchCategories,
    {
      refetchOnMount: 'always',
      ...options,
    }
  );
};
