import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const viewUserProfile = async (): Promise<AxiosResponse> => {
  return await api.get(API_PATHS.USER_PROFILE);
};

export const userLicenseUpdate = async (data: {
  license: string;
  format_id: string;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.USER_LICENSE_UPDATE, { data });
};
