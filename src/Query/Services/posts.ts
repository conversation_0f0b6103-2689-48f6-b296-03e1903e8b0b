import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { PostsResponseData } from '@Redux/SystemControl';
import { AxiosResponse } from 'axios';

const api = new Api();
export const getPosts = async (): Promise<
  AxiosResponse<PostsResponseData, unknown>
> => {
  const response: AxiosResponse<PostsResponseData, unknown> = await api.get(
    API_PATHS.EXAMPLE
  );
  return response; // Ensure this returns data in the expected format (e.g., an array of posts).
};
