import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getSubscriptionWiseMembers = async (
  data: any
): Promise<AxiosResponse> => {
  return api.get(API_PATHS.GET_MEMBER_LIST, {
    params: { ...data },
  });
};

export const purchaseMembers = async (data: any): Promise<AxiosResponse> => {
  return api.post(API_PATHS.PURCHASE_UPLOAD_REPORT, {
    data,
  });
};
