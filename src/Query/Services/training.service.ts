import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const fetchVideoListings = async (): Promise<AxiosResponse> => {
  return await api.get(API_PATHS.VIDEO_LISTINGS);
};

export const getVideoDetails = async (
  videoId: number
): Promise<AxiosResponse> => {
  return await api.get(`${API_PATHS.VIDEO_DETAILS}?video_id=${videoId}`);
};

export const getQuizDetails = async (
  videoId: number
): Promise<AxiosResponse> => {
  return await api.get(`${API_PATHS.QUIZ_LISTING}?video_id=${videoId}`);
};

export const getAnswers = async (quizId: number): Promise<AxiosResponse> => {
  return await api.get(`${API_PATHS.QUIZ_ANSWERS}?quiz_id=${quizId}`);
};

export const submitQuiz = async (data: any): Promise<AxiosResponse> => {
  return api.post(API_PATHS.QUIZ_SUBMIT, {
    data: data,
  });
};

export const purchaseVideo = async (data: any): Promise<AxiosResponse> => {
  return api.post(API_PATHS.VIDEO_PURCHASE, {
    data: data,
  });
};

export const purchaseUploadReportVideo = async (
  data: any
): Promise<AxiosResponse> => {
  return api.post(API_PATHS.PURCHASE_UPLOAD_REPORT, {
    data,
  });
};

export const fetchCategories = async (): Promise<AxiosResponse> => {
  const api = new Api();
  return await api.get('training-education/list-category/');
};

export const fetchCategoryVideos = async (
  categoryId: string | number | null,
  page: number
): Promise<AxiosResponse> => {
  const api = new Api();
  const url = 'training-education/category-videos/';
  const params = new URLSearchParams();

  params.append('page', page.toString());

  if (categoryId && categoryId !== 'all') {
    params.append('category_id', categoryId.toString());
  }

  return await api.get(`${url}?${params.toString()}`);
};
