/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UserData {
  id: number;
  org_id: number;
  first_name: string;
  last_name: string;
  email: string;
  mobile_no: string;
  country_code: string;
  role: string;
  verify_token: string;
  user_license: string;
  state: number;
  state_name: string;
  country: number;
  country_name: string;
  format_id: number;
  prefix: string;
  postfix: string | null;
  license_status: string;
  is_email_notification: boolean;
  is_push_notification: boolean;
  is_subscribed: boolean;
  is_subscription_cancel: boolean;
  profile_picture: string;
  subscription_data: any;
  access_token?: string;
  additional_report_price?: number;
  additional_user_price?: number;
  purchase_report_product_id?: string;
  purchase_user_product_id?: string;
}

// Define initial state
interface UserState {
  user: UserData | null | any;
}

const initialState: UserState = {
  user: null,
};

const userSlice = createSlice({
  name: 'userControl',
  initialState,
  reducers: {
    setUserData: (state, action: PayloadAction<UserData>) => {
      state.user = action.payload;
    },
    clearUserData: (state) => {
      state.user = null;
    },
  },
});

export const { setUserData, clearUserData } = userSlice.actions;
export default userSlice.reducer;
