{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/Components/Common/AccessibleImage.tsx", "./src/Components/Common/TempComponent.tsx", "./src/Components/Common/index.ts", "./src/Components/Common/ActionButtons/index.tsx", "./src/Components/Common/DocDetailCard/index.tsx", "./src/Components/Common/ExpandableText/index.tsx", "./src/Components/Common/HistoryCard/index.tsx", "./src/Components/Common/MainLoader/index.tsx", "./src/Components/Common/Popover/index.tsx", "./src/Components/Layout/index.ts", "./src/Components/Layout/ContentLayout/index.tsx", "./src/Components/Layout/MainLayout/MenuItem.tsx", "./src/Components/Layout/MainLayout/Sidebar.tsx", "./src/Components/Layout/MainLayout/index.tsx", "./src/Components/Layout/ProfileLayout/index.tsx", "./src/Components/Layout/RegisterLayout/index.tsx", "./src/Components/Recorder/index.tsx", "./src/Components/UI/index.ts", "./src/Components/UI/AccessibleImage/index.tsx", "./src/Components/UI/Accordion/index.tsx", "./src/Components/UI/Breadcrumb/index.tsx", "./src/Components/UI/Button/index.tsx", "./src/Components/UI/Checkbox/index.tsx", "./src/Components/UI/DatePicker/index.tsx", "./src/Components/UI/InputField/TextAreaField.tsx", "./src/Components/UI/InputField/index.tsx", "./src/Components/UI/InputSelect/index.tsx", "./src/Components/UI/Modal/index.tsx", "./src/Components/UI/Pagination/index.tsx", "./src/Components/UI/Popover/index.tsx", "./src/Components/UI/Skeleton/index.tsx", "./src/Components/UI/Switch/index.tsx", "./src/Components/UI/Table/FilterPopover.tsx", "./src/Components/UI/Table/SelectFilter.tsx", "./src/Components/UI/Table/index.tsx", "./src/Components/UI/Toast/ToastProvider.tsx", "./src/Components/UI/Toast/index.tsx", "./src/Components/UI/Tooltip/index.tsx", "./src/Components/UI/VIewMoreText/index.tsx", "./src/Config/Path.Config.ts", "./src/Config/Routes.Config.ts", "./src/Config/firebaseConfig.ts", "./src/Helpers/Api.ts", "./src/Helpers/CacheKeys.ts", "./src/Helpers/Constants.ts", "./src/Helpers/Paths.ts", "./src/Helpers/PhoneCountryCodes.ts", "./src/Helpers/QueryClient.ts", "./src/Helpers/Roleguard.tsx", "./src/Helpers/StatusCodes.ts", "./src/Helpers/Utils.ts", "./src/Hooks/useAnalytics.ts", "./src/Hooks/useAppDispatch.ts", "./src/Hooks/useDebouce.ts", "./src/Hooks/useHasAccess.ts", "./src/Hooks/useScreenScale.ts", "./src/Icons/index.ts", "./src/Icons/AboutUs/index.tsx", "./src/Icons/AddIcon/index.tsx", "./src/Icons/AdminIcon/index.tsx", "./src/Icons/ArrowLeftIcon/index.tsx", "./src/Icons/AscSortIcon/index.tsx", "./src/Icons/AttachmentIcon/index.tsx", "./src/Icons/BackArrowIcon/index.tsx", "./src/Icons/BackIcon/index.tsx", "./src/Icons/BellIcon/index.tsx", "./src/Icons/BellRingingIcon/index.tsx", "./src/Icons/CancelIcon/index.tsx", "./src/Icons/CaretRightIcon/index.tsx", "./src/Icons/ChartLineUpIcon/index.tsx", "./src/Icons/CirclePlusIcon/index.tsx", "./src/Icons/CircleTickIcon/index.tsx", "./src/Icons/ClockCounterwiseIcon/index.tsx", "./src/Icons/ClockIcon/index.tsx", "./src/Icons/CloseIcon/index.tsx", "./src/Icons/CommentIcon/index.tsx", "./src/Icons/DatabaseIcon/index.tsx", "./src/Icons/DeleteAccountIcon/index.tsx", "./src/Icons/DeleteIcon/index.tsx", "./src/Icons/DownloadIcon/index.tsx", "./src/Icons/DscSortIcon/index.tsx", "./src/Icons/DurationIcon/index.tsx", "./src/Icons/EditIcon/index.tsx", "./src/Icons/EnterpriseIcon/index.tsx", "./src/Icons/ErrorIcon/index.tsx", "./src/Icons/ExportIcon/index.tsx", "./src/Icons/EyeIcon/index.tsx", "./src/Icons/FailedIcon/index.tsx", "./src/Icons/FileIcon/index.tsx", "./src/Icons/FilterIcon/index.tsx", "./src/Icons/HouseIcon/index.tsx", "./src/Icons/InfoIcon/index.tsx", "./src/Icons/KeyIcon/index.tsx", "./src/Icons/Loader/index.tsx", "./src/Icons/LogoutIcon/index.tsx", "./src/Icons/MenuIcon/index.tsx", "./src/Icons/MicIcon/index.tsx", "./src/Icons/MicOffIcon/index.tsx", "./src/Icons/NewChatIcon/index.tsx", "./src/Icons/NoSortIcon/index.tsx", "./src/Icons/NotepadIcon/index.tsx", "./src/Icons/PdfIcon/index.tsx", "./src/Icons/PhoneCallIcon/index.tsx", "./src/Icons/PlayIcon/index.tsx", "./src/Icons/PrintIcon/index.tsx", "./src/Icons/PrivacyPolicy/index.tsx", "./src/Icons/ProcessingIcon/index.tsx", "./src/Icons/PurchaseHistoryIcon/index.tsx", "./src/Icons/QuestionsIcon/index.tsx", "./src/Icons/ReportIcon/index.tsx", "./src/Icons/RoboIcon/index.tsx", "./src/Icons/SearchIcon/index.tsx", "./src/Icons/SettingIcon/index.tsx", "./src/Icons/SimpleTickIcon/index.tsx", "./src/Icons/TermsAndConditionIcon/index.tsx", "./src/Icons/ThreeDotIcon/index.tsx", "./src/Icons/TickIcon/index.tsx", "./src/Icons/UpgradeIcon/index.tsx", "./src/Icons/UploadIcon/index.tsx", "./src/Icons/UserCircleIcon/index.tsx", "./src/Icons/UserCircleMinusIcon/index.tsx", "./src/Icons/UserCirclePlusIcon/index.tsx", "./src/Icons/VideoFinishIcon/index.tsx", "./src/Icons/VideoIcon/index.tsx", "./src/Icons/VideoLockIcon/index.tsx", "./src/Icons/VideoUserIcon/index.tsx", "./src/Icons/WarningIcon/index.tsx", "./src/LanguageProvider/i18n.ts", "./src/Pages/index.ts", "./src/Pages/AboutUs/index.tsx", "./src/Pages/AboutUsPublic/index.tsx", "./src/Pages/Analytics/index.tsx", "./src/Pages/BuilderHub/AddQuestionModal.tsx", "./src/Pages/BuilderHub/CodeOfConductModal.tsx", "./src/Pages/BuilderHub/CommentSection.tsx", "./src/Pages/BuilderHub/ReportModal.tsx", "./src/Pages/BuilderHub/SinglePost.tsx", "./src/Pages/BuilderHub/index.tsx", "./src/Pages/BusinessDetails/index.tsx", "./src/Pages/ContactUs/index.tsx", "./src/Pages/Disclaimer/index.tsx", "./src/Pages/Enterprise/index.tsx", "./src/Pages/Enterprise/AddMembers/index.tsx", "./src/Pages/Forms&Documents/DetailsOfDocument.tsx", "./src/Pages/Forms&Documents/EnterpriseLogoModal.tsx", "./src/Pages/Forms&Documents/PDFViewer.tsx", "./src/Pages/Forms&Documents/index.tsx", "./src/Pages/History/index.tsx", "./src/Pages/Home/index.tsx", "./src/Pages/InternetConnectionMonitor/index.tsx", "./src/Pages/LicenseCheck/index.tsx", "./src/Pages/Login/index.tsx", "./src/Pages/Members/index.tsx", "./src/Pages/MySubscription/index.tsx", "./src/Pages/NotificationSettings/index.tsx", "./src/Pages/Notifications/index.tsx", "./src/Pages/PageNotFound/index.tsx", "./src/Pages/PerformanceSolution/CodeOfConductModal.tsx", "./src/Pages/PerformanceSolution/index.tsx", "./src/Pages/PrivacyPolicy/index.tsx", "./src/Pages/PrivacyPolicyPublic/index.tsx", "./src/Pages/Profile/index.tsx", "./src/Pages/Redirecting/index.tsx", "./src/Pages/Register/EnterpriseRegisterForm.tsx", "./src/Pages/Register/index.tsx", "./src/Pages/Subscribe/SalesContactForm.tsx", "./src/Pages/Subscribe/index.tsx", "./src/Pages/TermsConditions/index.tsx", "./src/Pages/TermsConditionsPublic/index.tsx", "./src/Pages/TrainingAndEducation/CategoryVideo.tsx", "./src/Pages/TrainingAndEducation/ProgressBar.tsx", "./src/Pages/TrainingAndEducation/QuestionDisplay.tsx", "./src/Pages/TrainingAndEducation/QuestionsList.tsx", "./src/Pages/TrainingAndEducation/QuizPage.tsx", "./src/Pages/TrainingAndEducation/ResultPage.tsx", "./src/Pages/TrainingAndEducation/VideoCard.tsx", "./src/Pages/TrainingAndEducation/WatchVideo.tsx", "./src/Pages/TrainingAndEducation/index.tsx", "./src/Pages/VerifyOtp/index.tsx", "./src/Query/Hooks/useAdmin.ts", "./src/Query/Hooks/useAuth.ts", "./src/Query/Hooks/useEnterprise.ts", "./src/Query/Hooks/useLocalStorageListener.ts", "./src/Query/Hooks/useMembers.ts", "./src/Query/Hooks/usePosts.ts", "./src/Query/Hooks/useProfile.ts", "./src/Query/Hooks/useTraining.ts", "./src/Query/Services/admin.service.ts", "./src/Query/Services/analytics.service.ts", "./src/Query/Services/auth.service.ts", "./src/Query/Services/enterprise.service.ts", "./src/Query/Services/posts.ts", "./src/Query/Services/profile.service.ts", "./src/Query/Services/subscription.service.ts", "./src/Query/Services/training.service.ts", "./src/Redux/Reducers.ts", "./src/Redux/store.ts", "./src/Redux/General/Toast.ts", "./src/Redux/SystemControl/PostControl.ts", "./src/Redux/SystemControl/UserControle.ts", "./src/Redux/SystemControl/index.ts"], "errors": true, "version": "5.6.3"}